/* eslint-disable */
/* tslint:disable */
// @ts-nocheck
/*
 * ---------------------------------------------------------------
 * ## THIS FILE WAS GENERATED VIA SWAGGER-TYPESCRIPT-API        ##
 * ##                                                           ##
 * ## AUTHOR: acacode                                           ##
 * ## SOURCE: https://github.com/acacode/swagger-typescript-api ##
 * ---------------------------------------------------------------
 */

import { BotControllerSendMessageData, SendMessageDto } from "./data-contracts";
import { ContentType, HttpClient, RequestParams } from "./http-client";

export class Bot<
  SecurityDataType = unknown,
> extends HttpClient<SecurityDataType> {
  /**
   * No description
   *
   * @tags Bot
   * @name Bot<PERSON>ontrollerSendMessage
   * @request POST:/bot/send-message
   * @response `200` `BotControllerSendMessageData` Message sent successfully
   * @response `201` `SendMessageResponseDto`
   * @response `400` `void` Invalid request parameters
   */
  botControllerSendMessage = (
    data: SendMessageDto,
    params: RequestParams = {},
  ) =>
    this.request<BotControllerSendMessageData, void>({
      path: `/bot/send-message`,
      method: "POST",
      body: data,
      type: ContentType.Json,
      format: "json",
      ...params,
    });
}
