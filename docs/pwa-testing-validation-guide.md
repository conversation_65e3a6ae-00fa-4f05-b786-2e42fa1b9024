# PWA Testing & Validation Guide

## Overview

This guide provides comprehensive testing procedures to ensure your IndianCashback PWA meets all requirements for Google Play Store deployment.

## 1. Local Development Testing

### Start Development Server
```bash
npm run dev
```

### Test PWA Features
1. **Service Worker Registration**
   - Open DevTools > Application > Service Workers
   - Verify service worker is registered and active
   - Check for any registration errors

2. **Manifest Validation**
   - Open DevTools > Application > Manifest
   - Verify all fields are populated correctly
   - Check icon loading and sizes

3. **Install Prompt**
   - Wait for install prompt to appear (after 500ms)
   - Test "Install Now" functionality
   - Test "Maybe Later" functionality

## 2. Production Build Testing

### Build and Test
```bash
npm run build
npm run start
```

### Lighthouse Audit
```bash
# Install Lighthouse CLI
npm install -g lighthouse

# Run PWA audit
lighthouse https://localhost:3000 --only-categories=pwa --view

# Run full audit
lighthouse https://localhost:3000 --view
```

### Target Scores
- **PWA Score**: 90+ (Required)
- **Performance**: 80+ (Recommended)
- **Accessibility**: 90+ (Recommended)
- **Best Practices**: 90+ (Recommended)
- **SEO**: 90+ (Recommended)

## 3. PWA Criteria Validation

### Required PWA Features ✅
- [x] **Web App Manifest**: `/manifest.json` accessible
- [x] **Service Worker**: Registered and caching resources
- [x] **HTTPS**: Served over secure connection
- [x] **Responsive Design**: Works on mobile and desktop
- [x] **Offline Functionality**: Basic offline support via service worker

### Enhanced PWA Features ✅
- [x] **Install Prompt**: Custom install prompt implemented
- [x] **Splash Screen**: Configured via manifest
- [x] **Theme Colors**: Consistent theme colors set
- [x] **Icons**: All required sizes available
- [x] **Shortcuts**: App shortcuts configured

## 4. Cross-Browser Testing

### Desktop Browsers
- **Chrome**: Full PWA support
- **Edge**: Full PWA support
- **Firefox**: Limited PWA support
- **Safari**: Basic PWA support

### Mobile Browsers
- **Chrome Android**: Full PWA support + install
- **Samsung Internet**: Full PWA support
- **Safari iOS**: Limited PWA support
- **Firefox Mobile**: Basic PWA support

### Testing Checklist
- [ ] App loads correctly in all browsers
- [ ] Install prompt works (Chrome/Edge)
- [ ] Service worker functions properly
- [ ] Offline functionality works
- [ ] Icons display correctly
- [ ] Theme colors applied

## 5. Mobile Device Testing

### Android Testing
```bash
# Connect Android device via USB
adb devices

# Open Chrome and navigate to your PWA
# Test install prompt and functionality
```

### iOS Testing
- Open Safari on iOS device
- Navigate to your PWA
- Test "Add to Home Screen" functionality
- Verify app behavior when launched from home screen

### Key Mobile Tests
- [ ] Touch interactions work properly
- [ ] Viewport scaling is correct
- [ ] Install/Add to Home Screen works
- [ ] App launches in standalone mode
- [ ] Navigation is touch-friendly
- [ ] Performance is acceptable on slower devices

## 6. Network Condition Testing

### Offline Testing
1. Load the app while online
2. Disconnect from internet
3. Navigate through cached pages
4. Verify offline fallback pages work
5. Test service worker cache strategies

### Slow Network Testing
1. Use Chrome DevTools Network tab
2. Throttle to "Slow 3G"
3. Test app loading and functionality
4. Verify progressive loading works

### Testing Commands
```bash
# Test offline functionality
# In Chrome DevTools:
# 1. Application > Service Workers > Offline checkbox
# 2. Network > Offline checkbox
# 3. Navigate through app
```

## 7. Performance Testing

### Core Web Vitals
- **Largest Contentful Paint (LCP)**: < 2.5s
- **First Input Delay (FID)**: < 100ms
- **Cumulative Layout Shift (CLS)**: < 0.1

### Performance Testing Tools
```bash
# Lighthouse performance audit
lighthouse https://localhost:3000 --only-categories=performance

# WebPageTest
# Visit https://www.webpagetest.org/
# Enter your URL and run test
```

### Performance Checklist
- [ ] Initial page load < 3 seconds
- [ ] Time to Interactive < 5 seconds
- [ ] Service worker caches resources efficiently
- [ ] Images are optimized
- [ ] JavaScript bundles are optimized

## 8. Security Testing

### HTTPS Validation
```bash
# Check SSL certificate
openssl s_client -connect www.indiancashback.com:443

# Verify security headers
curl -I https://www.indiancashback.com
```

### Security Headers Checklist
- [x] **X-Frame-Options**: DENY
- [x] **X-Content-Type-Options**: nosniff
- [x] **Referrer-Policy**: strict-origin-when-cross-origin
- [x] **Permissions-Policy**: Configured appropriately

### Content Security Policy
- Test for XSS vulnerabilities
- Verify external resource loading
- Check for mixed content issues

## 9. Accessibility Testing

### Automated Testing
```bash
# Install axe-core CLI
npm install -g @axe-core/cli

# Run accessibility audit
axe https://localhost:3000
```

### Manual Testing
- [ ] Keyboard navigation works
- [ ] Screen reader compatibility
- [ ] Color contrast meets WCAG standards
- [ ] Focus indicators are visible
- [ ] Alt text for images

### Tools
- **Lighthouse Accessibility Audit**
- **axe DevTools Extension**
- **WAVE Web Accessibility Evaluator**
- **Screen Reader Testing** (NVDA, JAWS, VoiceOver)

## 10. TWA-Specific Testing

### Digital Asset Links
```bash
# Verify asset links file
curl https://www.indiancashback.com/.well-known/assetlinks.json

# Validate JSON format
cat assetlinks.json | jq .
```

### TWA Functionality
- [ ] App opens without browser UI
- [ ] Deep links work correctly
- [ ] Share target functionality works
- [ ] Custom tabs fallback works
- [ ] App shortcuts function properly

## 11. User Experience Testing

### Install Flow Testing
1. **First Visit**: Install prompt appears after delay
2. **Install Process**: Smooth installation experience
3. **Launch Experience**: App opens in standalone mode
4. **Uninstall Process**: Easy to uninstall if needed

### Navigation Testing
- [ ] Bottom navigation works on mobile
- [ ] Header navigation works on desktop
- [ ] Back button behavior is correct
- [ ] Deep linking works properly

### Feature Testing
- [ ] Search functionality works
- [ ] User authentication works
- [ ] Cashback tracking works
- [ ] Store browsing works
- [ ] Earnings dashboard works

## 12. Automated Testing Setup

### Jest + Testing Library
```bash
# Install testing dependencies
npm install --save-dev @testing-library/react @testing-library/jest-dom jest-environment-jsdom

# Run tests
npm test
```

### Playwright E2E Testing
```bash
# Install Playwright
npm install --save-dev @playwright/test

# Run E2E tests
npx playwright test
```

### Example Test Cases
```javascript
// PWA install prompt test
test('should show install prompt after delay', async () => {
  // Test implementation
});

// Service worker test
test('should register service worker', async () => {
  // Test implementation
});

// Offline functionality test
test('should work offline', async () => {
  // Test implementation
});
```

## 13. Pre-Deployment Validation

### Final Checklist
- [ ] All PWA criteria met (Lighthouse score 90+)
- [ ] Cross-browser testing completed
- [ ] Mobile device testing completed
- [ ] Performance targets met
- [ ] Security headers configured
- [ ] Accessibility standards met
- [ ] TWA functionality tested
- [ ] User experience validated

### Production Environment Testing
```bash
# Test production build locally
npm run build
npm run start

# Test on staging environment
# Deploy to staging and run full test suite

# Test on production environment
# After deployment, run smoke tests
```

## 14. Monitoring and Analytics

### Setup Analytics
- Google Analytics configured ✅
- PWA install tracking
- User engagement metrics
- Performance monitoring

### Error Monitoring
```javascript
// Service worker error handling
self.addEventListener('error', (event) => {
  console.error('Service worker error:', event.error);
  // Send to error tracking service
});
```

### Key Metrics to Track
- PWA install rate
- User retention
- Page load times
- Error rates
- Offline usage

## 15. Troubleshooting Common Issues

### Service Worker Issues
```bash
# Clear service worker cache
# In DevTools: Application > Storage > Clear storage

# Force update service worker
# In DevTools: Application > Service Workers > Update
```

### Manifest Issues
- Verify manifest.json is accessible
- Check icon paths are correct
- Validate JSON syntax
- Ensure MIME type is correct

### Install Prompt Issues
- Check beforeinstallprompt event handling
- Verify PWA criteria are met
- Test on supported browsers only
- Clear browser data and retry

---

**Remember**: Thorough testing is crucial for a successful PWA deployment. Take time to test all features across different devices and network conditions before submitting to the Play Store.
