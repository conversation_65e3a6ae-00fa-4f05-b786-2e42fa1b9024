import React from 'react';

// Section components (to be implemented in app/components/referral/)
import HowItWorksMain from '../components/referral-and-link-generator/HowItWorks';
import ReferFriendFAQ from '../components/referral-and-link-generator/ReferFriendFAQ';
import LinkGeneratorHero from '../components/referral-and-link-generator/LinkGeneratorHero';
import LinkGeneratorPerformance from '../components/referral-and-link-generator/LinkGeneratorPerformance';
import SupportedStores from '../components/referral-and-link-generator/SupportedStores';
import CalculateMonthlyProfit from '../components/referral-and-link-generator/CalculateMonthlyProfit';
import CommonHeader from '../components/headers/common-header';

const IndexClientsLinkGenerator = () => {
  return (
    <>
      <CommonHeader headline='Share & Earn' />{' '}
      <div className='w-full min-h-screen bg-background p-3 flex flex-col justify-start gap-y-4'>
        <LinkGeneratorHero />
        <SupportedStores />
        <CalculateMonthlyProfit />

        <HowItWorksMain type='link-generator' />
        <LinkGeneratorPerformance />
        <ReferFriendFAQ type='link-generator' />
      </div>
    </>
  );
};

export default IndexClientsLinkGenerator;
