'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import React, { useState } from 'react';
import MainOfferCard from '@/app/components/cards/main-offer-card';
import PlusIcon from '@/app/components/svg/plus-icon';
import CommonToolbar from '@/app/components/common-toolbar';
import CommonFilterSidebar from '../components/misc/common-filter-sidebar';
import Image from 'next/image';
import CommonFilterMobile from '../components/misc/common-filter-mobile';
import type { OngoingOffersResponse } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  useCreateMultiQueryString,
  useResponsiveGrid,
} from '@/utils/custom-hooks';
import EnhancedNoData from '../components/enhanced-no-data';

const IndexClients = ({ data }: { data: OngoingOffersResponse }) => {
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);

  // Use our custom responsive grid hook
  const { getGridProps } = useResponsiveGrid();

  return (
    <>
      <CommonHeader
        backRoute='/'
        headline='On Going Sale Deals'
        showMenuBtn
        subHeading={
          <>
            <span>Results</span>
            <span className='ml-[6px] text-[7px] sm:text-[8px] font-nexa'>
              {`${data.pagination.pageSize}/${data.pagination.total}`}
            </span>
          </>
        }
      />
      <div className='flex h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] !mb-0 gap-x-[8px]'>
        <aside className='shrink-0 w-[270px] lg:max-h-[calc(100svh-112px)] sticky top-[112px] overflow-hidden  hidden lg:block scrollbarNone'>
          <CommonFilterSidebar
            filterProps={[{ filter: 'sale' }, { filter: 'offer' }]}
            rootClass='!mt-0'
          />
        </aside>
        <div className='w-full'>
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'On Going Sale Deals' },
            ]}
          />
          <section>
            <CommonToolbar
              onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
              rootClassName='shadow-none lg:shadow'
            />
            <div className='bg-container mx-[6px] px-[8px] lg:mx-0 lg:pt-[16px] lg:px-[40px] pb-[18px]'>
              <h4 className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite'>
                Results{' '}
                <span className='text-xs font-nexa font-[800]'>
                  ({data.pagination.total})
                </span>
              </h4>
              {data?.offers?.length ? (
                <div {...getGridProps()}>
                  {data.offers.map((item) => {
                    return (
                      <MainOfferCard
                        duration={item.endDate}
                        hideCbTag={item?.hideCbTag}
                        isOfferUpto={Boolean(item?.offerCaption?.trim())}
                        key={item.uid}
                        offerTitle={item.offerTitle}
                        productImgUrl={item.productImage || ''}
                        rootClass='!w-full'
                        saved={item.saved}
                        showNewBadge={false}
                        storeImgUrl={item.storeLogoUrl}
                        storeName={item.storeName}
                        uid={item.uid}
                      >
                        {/* first child */}
                        <p
                          dangerouslySetInnerHTML={{
                            __html: item?.offerTitle ?? '',
                          }}
                        />
                        {/* second child */}
                        {item.salePrice && item.salePrice > 0 && (
                          <p className='text-primary font-medium text-[9px]'>
                            Offer Applied Price
                            <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                              {formatIndRs(item?.salePrice)}
                            </span>
                          </p>
                        )}

                        {/* this child added for order wrong bug in ongoing sale offers */}
                        <></>

                        {/* third child */}
                        {!item?.hideCbTag ? (
                          <>
                            <PlusIcon className='text-black' />
                            <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                              {item.offerCaption}
                            </p>
                          </>
                        ) : (
                          <></>
                        )}

                        {/* fourth child */}
                        <div className=' h-full flex items-center justify-center'>
                          <Image
                            alt='img'
                            height={22}
                            src={item.saleLogoUrl ?? ''}
                            width={22}
                          />
                          <span className='ml-[5px] text-[9px] font-[300] text-content'>
                            {item.saleCaption}
                          </span>
                        </div>
                      </MainOfferCard>
                    );
                  })}
                </div>
              ) : (
                <EnhancedNoData
                  customHeight='min-h-[400px]'
                  message='No deals available at the moment. Please check back later!'
                  showHomeLink={false}
                />
              )}
              {data.pagination.pageSize > 0 && (
                <div className='flex-center w-full my-[50px]'>
                  <ConfigProvider
                    theme={{
                      algorithm:
                        resolvedTheme === 'dark'
                          ? theme.darkAlgorithm
                          : theme.defaultAlgorithm,
                    }}
                  >
                    <Pagination
                      defaultCurrent={1}
                      defaultPageSize={15}
                      onChange={(pageNumber, pageSize) =>
                        replace(
                          `${pathname}?${createMultiQueryString([
                            { name: 'page', value: pageNumber.toString() },
                            { name: 'pageSize', value: pageSize.toString() },
                          ])}`
                        )
                      }
                      total={data.pagination.total}
                    />
                  </ConfigProvider>
                </div>
              )}
            </div>
          </section>
        </div>
      </div>
      <CommonFilterMobile
        filterProps={[{ filter: 'sale' }, { filter: 'offer' }]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexClients;
