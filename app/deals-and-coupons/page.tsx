import { BASE_URL } from '@/config';
import IndexClients from './index-clients';
import type { DealAndCouponsResponse } from '@/services/api/data-contracts';
import type { CustomSearchParamsTypes } from '@/types/global-types';
import { getCookie } from 'cookies-next';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type { Metadata } from 'next';

export const metadata: Metadata = {
  title: 'Best Ongoing Deals & Coupons – Save More with Cashback!',
  description:
    'Find the latest active deals, discount coupons & cashback offers from top e-commerce stores. Save big on every purchase with IndianCashback!',
  alternates: {
    canonical: 'https://www.indiancashback.com/deals-and-coupons',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/deals-and-coupons',
    title: 'Best Ongoing Deals & Coupons – Save More with Cashback!',
    description:
      'Find the latest active deals, discount coupons & cashback offers from top e-commerce stores. Save big on every purchase with IndianCashback!',
  },
};

async function getDealsCouponsData(searchParams: CustomSearchParamsTypes) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    userType = 'both',
    offerType = 'deals',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const accessToken = getCookie('accessToken', { cookies }) as string;
  return await fetchWrapper<DealAndCouponsResponse>(
    `${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&userType=${userType}&offerType=${offerType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token: accessToken,
      cache: 'no-store',
    }
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: CustomSearchParamsTypes;
}) => {
  let resData: DealAndCouponsResponse;
  try {
    resData = await getDealsCouponsData(searchParams);
    console.log('Deals and Coupons Data:', resData.offers);
  } catch (err: any) {
    console.log({ err });
    return (
      <div className='error-container'>
        <h1>Error loading deals and coupons</h1>
        <p>Please try again later.</p>
      </div>
    );
  }
  return <IndexClients data={resData} />;
};

export default Page;

// Dynamic Configuration - using searchParams makes this page dynamic
export const dynamic = 'force-dynamic';
