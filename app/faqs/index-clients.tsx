'use client';
import React, { useState, useEffect, useMemo } from 'react';
import CommonHeader from '../components/headers/common-header';
import BreadcrumbSaveShare from '../components/atoms/breadcrumb-container';
import Image from 'next/image';
import FAQAccordion from '../components/accordians/faq-accordian';
import SearchInput from '../components/atoms/search-input';
import { motion } from 'framer-motion';
import { faqData, faqCategories, type FAQItem } from './faq-data';
import clsx from 'clsx';
import FaqSVG from '../components/svg/faq';
import { displayProtectedEmail } from '@/utils/email-protection';

const IndexClientsFAQ = () => {
  const [activeAccordionId, setActiveAccordionId] = useState<number | null>(
    null
  );
  const [searchValue, setSearchValue] = useState('');
  const [selectedCategory, setSelectedCategory] = useState<string | null>(null);
  const [filteredFAQs, setFilteredFAQs] = useState<FAQItem[]>(faqData);

  // Handle accordion toggle
  const handleToggle = (id: number) => {
    setActiveAccordionId(activeAccordionId === id ? null : id);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
  };

  // Handle category selection
  const handleCategorySelect = (category: string | null) => {
    setSelectedCategory(category);
    setActiveAccordionId(null); // Close any open accordion when changing category
  };

  // Filter FAQs based on search and category
  useEffect(() => {
    let filtered = faqData;

    // Filter by category if selected
    if (selectedCategory) {
      filtered = filtered.filter((faq) => faq.category === selectedCategory);
    }

    // Filter by search term if provided
    if (searchValue.trim()) {
      const searchTerm = searchValue.toLowerCase();
      filtered = filtered.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchTerm) ||
          faq.answer.toLowerCase().includes(searchTerm)
      );
    }

    setFilteredFAQs(filtered);
  }, [searchValue, selectedCategory]);

  // Group FAQs by category for display when no search is active
  const faqsByCategory = useMemo(() => {
    const grouped: Record<string, FAQItem[]> = {};

    faqCategories.forEach((category) => {
      grouped[category.name] = faqData.filter(
        (faq) => faq.category === category.name
      );
    });

    return grouped;
  }, []);

  // Determine if we should show "no results" message
  const showNoResults =
    filteredFAQs.length === 0 &&
    (searchValue.trim() !== '' || selectedCategory !== null);

  return (
    <>
      <CommonHeader headline='Frequently Asked Questions (FAQs)' />
      <section className='header-container'>
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            { title: 'FAQs' },
          ]}
        />

        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] bg-[#efefef] dark:bg-container'>
          {/* Hero Section */}
          <div className='text-center mb-8'>
            <div className='mx-auto w-[80px] h-[80px] lg:w-[120px] lg:h-[120px] relative mb-4'>
              <Image
                alt='FAQ'
                className='mx-auto'
                fill
                priority
                src='/img/faq-hero.png'
              />
            </div>
            <h1 className='text-xl lg:text-2xl font-bold text-blackWhite mb-2'>
              How can we help you?
            </h1>
            <p className='text-sm lg:text-base text-gray-600 dark:text-gray-300 max-w-2xl mx-auto'>
              Find answers to frequently asked questions about
              IndianCashback.com, how cashback works, and tips for maximizing
              your earnings.
            </p>
          </div>

          {/* Search Bar */}
          <div className='max-w-xl mx-auto mb-8'>
            <SearchInput
              onChange={handleSearchChange}
              onClose={() => setSearchValue('')}
              placeholder='Search for questions or keywords...'
              rootClass='w-full mx-auto'
              value={searchValue}
            />
          </div>

          {/* Category Pills */}
          <div className='flex flex-wrap justify-center gap-2 mb-8'>
            <motion.button
              className={clsx(
                'px-4 py-2 rounded-full text-sm font-medium transition-all',
                selectedCategory === null
                  ? 'bg-primary text-white'
                  : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
              )}
              onClick={() => handleCategorySelect(null)}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              All
            </motion.button>

            {faqCategories.map((category) => (
              <motion.button
                className={clsx(
                  'px-4 py-2 rounded-full text-sm font-medium transition-all flex items-center gap-2',
                  selectedCategory === category.name
                    ? 'bg-primary text-white'
                    : 'bg-gray-200 dark:bg-gray-700 text-gray-700 dark:text-gray-200 hover:bg-gray-300 dark:hover:bg-gray-600'
                )}
                key={category.id}
                onClick={() => handleCategorySelect(category.name)}
                whileHover={{ scale: 1.05 }}
                whileTap={{ scale: 0.95 }}
              >
                {category.icon && (
                  <Image
                    alt={category.name}
                    className='w-4 h-4'
                    height={16}
                    src={category.icon}
                    width={16}
                  />
                )}
                {category.name}
              </motion.button>
            ))}
          </div>

          {/* FAQ Content */}
          <div className='max-w-3xl mx-auto'>
            {/* Show filtered results when searching or category is selected */}
            {(searchValue.trim() !== '' || selectedCategory !== null) && (
              <div>
                {showNoResults ? (
                  <motion.div
                    animate={{ opacity: 1 }}
                    className='text-center py-8'
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    <FaqSVG className='w-12 h-12 mx-auto text-gray-400 mb-4' />
                    <h3 className='text-lg font-medium text-gray-700 dark:text-gray-300 mb-2'>
                      No results found
                    </h3>
                    <p className='text-gray-500 dark:text-gray-400'>
                      We couldn't find any FAQs matching your search. Try
                      different keywords or browse by category.
                    </p>
                  </motion.div>
                ) : (
                  <div>
                    <h2 className='text-lg font-semibold text-blackWhite mb-4'>
                      {searchValue
                        ? `Search Results (${filteredFAQs.length})`
                        : `${selectedCategory} (${filteredFAQs.length})`}
                    </h2>
                    {filteredFAQs.map((faq) => (
                      <FAQAccordion
                        activeId={activeAccordionId}
                        answer={faq.answer}
                        highlight={searchValue}
                        id={faq.id}
                        key={faq.id}
                        onClick={handleToggle}
                        question={faq.question}
                      />
                    ))}
                  </div>
                )}
              </div>
            )}

            {/* Show categorized FAQs when not searching */}
            {searchValue.trim() === '' && selectedCategory === null && (
              <div>
                {faqCategories.map((category) => {
                  const categoryFAQs = faqsByCategory[category.name];
                  if (!categoryFAQs || categoryFAQs.length === 0) {
                    return null;
                  }

                  return (
                    <div className='mb-8' key={category.id}>
                      <div className='flex items-center gap-2 mb-4'>
                        {category.icon && (
                          <Image
                            alt={category.name}
                            className='w-6 h-6'
                            height={24}
                            src={category.icon}
                            width={24}
                          />
                        )}
                        <h2 className='text-lg font-semibold text-blackWhite'>
                          {category.name}
                        </h2>
                      </div>

                      {categoryFAQs.map((faq) => (
                        <FAQAccordion
                          activeId={activeAccordionId}
                          answer={faq.answer}
                          id={faq.id}
                          key={faq.id}
                          onClick={handleToggle}
                          question={faq.question}
                        />
                      ))}
                    </div>
                  );
                })}
              </div>
            )}
          </div>

          {/* Contact Support Section */}
          <div className='mt-12 bg-gray-100 dark:bg-gray-800 rounded-lg p-6 text-center max-w-3xl mx-auto'>
            <h3 className='text-lg font-semibold text-blackWhite mb-2'>
              Still have questions?
            </h3>
            <p className='text-gray-600 dark:text-gray-300 mb-4'>
              If you couldn't find the answer you were looking for, our support
              team is here to help.
            </p>
            <motion.a
              className='bg-primary text-white px-6 py-3 rounded-md font-medium'
              href='mailto:<EMAIL>'
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              Contact Support
            </motion.a>
            <p className='mt-4 text-xs text-gray-500 dark:text-gray-400'>
              {displayProtectedEmail('<EMAIL>')}
            </p>
          </div>
        </div>
      </section>
    </>
  );
};

export default IndexClientsFAQ;
