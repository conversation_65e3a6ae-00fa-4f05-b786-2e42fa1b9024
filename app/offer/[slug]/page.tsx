import React from 'react';
import IndexClients from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import {
  ContextOfferDealsType,
  GetOfferByIdResponse,
} from '@/services/api/data-contracts';
import { BASE_URL } from '@/config';
import { Metadata } from 'next';
import { generateProductSchema } from '@/utils/schema';

// Generate static params for popular offers
export async function generateStaticParams() {
  try {
    // Fetch trending offers for static generation
    const offersData = await fetchWrapper<any>(`${BASE_URL}/context/offers`, {
      // Use a longer cache for build-time generation
      next: { revalidate: 3600 }, // 1 hour
    });

    // Generate params for trending offers (limit to avoid build time issues)
    // The API returns { deals: [...], coupons: [...] } structure
    const deals = offersData?.deals || [];
    const coupons = offersData?.coupons || [];

    // Combine deals and coupons for static generation
    const allOffers = [...deals, ...coupons];

    // Ensure we have an array before calling slice
    if (!Array.isArray(allOffers) || allOffers.length === 0) {
      console.warn('No offers found for static generation');
      return [];
    }

    return allOffers.slice(0, 20).map((offer: any) => ({
      slug: encodeURIComponent(offer?.offerTitle || ''),
    }));
  } catch (error) {
    console.error('Error generating static params for offers:', error);
    // Return empty array if API fails during build
    return [];
  }
}

export async function generateMetadata({
  params,
}: {
  params: { slug?: string };
}): Promise<Metadata> {
  try {
    if (!params.slug) {
      return {
        title: 'Offer Not Found',
        description: 'The requested offer could not be found.',
      };
    }

    const result = await getOffersData(params.slug);

    return {
      title: `${result.offer?.offerTitle || 'Offer'} - IndianCashback.com`,
      description:
        result.offer?.offerDescription ||
        'Get the best deals and cashbacks with IndianCashback.com',
      alternates: {
        canonical: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params.slug
        )}`,
      },
      openGraph: {
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params.slug
        )}`,
        title: result.offer?.offerTitle || 'Offer',
        description:
          result.offer?.offerDescription ||
          'Get the best deals and cashbacks with IndianCashback.com',
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Offer Not Found',
      description: 'The requested offer could not be found.',
    };
  }
}

async function getOffersData(path: string = '') {
  console.log('path:', path);
  const res = await fetchWrapper<GetOfferByIdResponse>(
    `${BASE_URL}/offers/offer/title/${path}`,
    {
      // Removed cache: 'no-store' to allow ISR caching
      // ISR will handle revalidation every 180 seconds
    }
  );
  return res;
}

const Page = async ({ params }: { params: { slug?: string } }) => {
  let data: ContextOfferDealsType[] = [];
  let offerData: GetOfferByIdResponse | null = null;

  try {
    const result = await getOffersData(params?.slug);
    offerData = result;
    data = result?.similarOffers as unknown as ContextOfferDealsType[];
  } catch (error) {
    console.error('Failed to fetch offers:', error);
  }

  // Generate JSON-LD structured data for the offer as a product
  let offerSchema = null;
  if (offerData?.offer) {
    const offer = offerData.offer;

    // Format price for the offer
    const priceValue = offer.offerAmount ? offer.offerAmount.toString() : '';

    offerSchema = generateProductSchema({
      name: offer.offerTitle,
      description:
        offer.offerDescription ||
        `${offer.offerTitle} - Get the best deals and cashbacks`,
      image: offer.productImage,
      brand: {
        name: offer.storeName,
      },
      offers: {
        price: priceValue,
        priceCurrency: 'INR',
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          params?.slug || ''
        )}`,
        ...(offer.endDate && { priceValidUntil: offer.endDate }),
      },
    });
  }

  return (
    <>
      {offerSchema && (
        <script
          dangerouslySetInnerHTML={{ __html: offerSchema }}
          type='application/ld+json'
        />
      )}
      <IndexClients data={data} />
    </>
  );
};

export default Page;

// ISR Configuration
// Revalidate every 3 minutes (180 seconds)
// Offers change frequently as they expire and new ones are added
export const revalidate = 180;

// Allow ISR for offers not in generateStaticParams
// This enables new offers to be statically generated on-demand
export const dynamicParams = true;
