'use client';
import React, { useState, useEffect } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import CommonHeader from '@/app/components/headers/common-header';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import InfoHighligtedCard from '@/app/components/user-page-components/info-highligted-card';
import ApprovedSVG from '@/app/components/svg/approved';
import LinkContainerWithoutCaption, {
  LinkContainerMyEarnings,
} from '@/app/components/my-earnings/link-container';
import Support24SVG from '@/app/components/svg/support24';
import FaqSVG from '@/app/components/svg/faq';
import ReferFriendSVG from '@/app/components/svg/refer-freind';
import WalletAddSVG from '@/app/components/svg/wallet-add';
import MyEarningsChart from '@/app/components/my-earnings/my-earnings-chart';
import TabsContainer from '@/app/components/atoms/tabs-container';
import <PERSON>b<PERSON><PERSON>oryAccordian from '@/app/components/accordians/cashback-history-accordian';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import NoData from '@/app/components/no-data';
import type {
  UserOverviewResponse,
  GetCbHistoryResponse,
} from '@/services/api/data-contracts';
import { StatusEnum1 } from '@/services/api/data-contracts';
import { formatIndRs } from '@/utils/helpers';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import { Gem, HandCoins } from 'lucide-react';
import PendingSVG from '@/app/components/svg/pending';
import CancelledSVG from '@/app/components/svg/cancelled';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setShowStoresFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/global-search-slice';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { ConfigProvider, type MenuProps, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';

// Sort items for cashback history
const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Oldest',
    key: 'oldest',
  },
  {
    label: 'Cashback Amount',
    key: 'cashbackAmount',
  },
];

const statusList: StatusEnum1[] = [
  StatusEnum1.Pending,
  StatusEnum1.Confirmed,
  StatusEnum1.Cancelled,
];

// Overview Tab Content
const OverviewTabContent = ({
  overviewData,
  router,
  onCashbackEarnedClick,
}: {
  overviewData: UserOverviewResponse;
  router: any;
  onCashbackEarnedClick: () => void;
}) => (
  <motion.div
    animate={{ opacity: 1, x: 0 }}
    className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full pb-[40px] px-2 md:px-4 lg:px-8'
    initial={{ opacity: 0, x: 20 }}
    transition={{ duration: 0.5, delay: 0.3 }}
  >
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='bg-container pt-[14px] px-[10px] pb-[30px] lg:pt-[24px] border-[0.5px] border-white dark:border-[#353943] rounded-[10px] w-full mx-auto mt-[23px]'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: 0.4 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <motion.div
        animate={{ opacity: 1 }}
        className='flex items-center justify-between px-[7px] lg:px-0 text-blackWhite'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5, delay: 0.5 }}
      >
        <motion.h4
          animate={{ opacity: 1, x: 0 }}
          className='text-xs lg:text-sm font-pat'
          initial={{ opacity: 0, x: -10 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          Overall Earnings
        </motion.h4>
      </motion.div>

      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='flex justify-center gap-x-[6px] lg:gap-x-[14px] mt-[13px] lg:mt-[6px]'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.7 }}
      >
        <InfoHighligtedCard
          caption='Pending'
          icon={<PendingSVG className='text-[#FFC554] w-[15px]' />}
          number={overviewData.totalPendingCount}
          onClick={() =>
            router.push(
              '/my-earnings-overview?tab=cashback-history&status=pending'
            )
          }
          stripColorClass='bg-[#FFC554]'
          tooltip='Cashback transactions waiting for approval from the store'
        />
        <InfoHighligtedCard
          caption='Confirmed'
          icon={<ApprovedSVG className='text-[#69C8B4] w-[15px]' />}
          number={overviewData.totalApprovedCount}
          onClick={() =>
            router.push(
              '/my-earnings-overview?tab=cashback-history&status=approved'
            )
          }
          stripColorClass='bg-[#69C8B4]'
          tooltip='Cashback transactions confirmed by the store and ready for withdrawal'
        />
        <InfoHighligtedCard
          caption='Cancelled'
          icon={<CancelledSVG className='text-[#F06B6B] w-[15px]' />}
          number={overviewData.totalCancelledCount}
          onClick={() =>
            router.push(
              '/my-earnings-overview?tab=cashback-history&status=cancelled'
            )
          }
          stripColorClass='bg-[#F06B6B]'
          tooltip='Cashback transactions that were rejected or cancelled by the store'
        />
      </motion.div>
      <motion.div
        animate={{ opacity: 1 }}
        className='flex flex-col items-center md:grid md:grid-cols-2 justify-items-center mt-[20px] gap-y-[7px] md:gap-[15px]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.totalCashbackEarned)}
          icon={
            <HandCoins className='w-[12px] lg:w-[18px] text-[#818181] dark:text-white' />
          }
          onClick={onCashbackEarnedClick}
          title='Total Cashback Earned'
        />
        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.flipkartRewardPoints)}
          icon={
            <Gem className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
          }
          title='Total Reward Points Earned'
        />

        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.shareAndEarnCashback)}
          icon={
            <HandCoins className='w-[12px] lg:w-[18px] text-[#818181] dark:text-white' />
          }
          onClick={onCashbackEarnedClick}
          title='Share and Earn Cashback Earned'
        />
        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.shareAndEarnRewards)}
          icon={
            <Gem className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
          }
          title='Share and Earn Reward Points Earned'
        />

        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.readyToWithdraw)}
          icon={
            <WalletAddSVG className='w-[12px] lg:w-[25px] text-[#818181] dark:text-white' />
          }
          onClick={() =>
            router.push('/payments?tab=cashback-history&status=confirmed')
          }
          title='Ready to Withdraw'
        />
        <LinkContainerMyEarnings
          amount={formatIndRs(overviewData.totalReferralCommission)}
          icon={
            <ReferFriendSVG className='w-[14px] lg:w-[19px] text-[#818181] dark:text-white' />
          }
          onClick={() => router.push('/referral-history')}
          title='Total Referral Commission Earned'
        />
      </motion.div>
    </motion.div>
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='bg-container pt-[14px] px-[10px] pb-[30px] lg:pt-[24px] border-[0.5px] border-white dark:border-[#353943] rounded-[10px] w-full mx-auto mt-[23px]'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: 0.9 }}
      whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
    >
      <MyEarningsChart
        monthlyCashback={overviewData.totalCashback}
        monthlyClicks={overviewData.totalClicks}
        monthlyOrderAmount={overviewData.totalOrderAmount}
      />
    </motion.div>
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className='flex flex-col md:flex-row md:justify-center gap-y-[13px] md:gap-x-[20px] mt-[30px]'
      initial={{ opacity: 0, y: 20 }}
      transition={{ duration: 0.5, delay: 1 }}
    >
      <LinkContainerWithoutCaption
        icon={
          <FaqSVG className='w-[15px] lg:w-[24px] text-[#818181] dark:text-white' />
        }
        onClick={() => router.push('/faqs')}
        title='Related Question and Answers'
      />
      <LinkContainerWithoutCaption
        icon={
          <Support24SVG className='w-[12px] lg:w-[20px] text-[#818181] dark:text-white' />
        }
        onClick={() => {
          router.push('https://tawk.to/indiancashback/');
        }}
        title='Support'
      />
    </motion.div>
  </motion.div>
);

// Cashback History Tab Content
const CashbackHistoryTabContent = ({
  cashbackHistoryData,
  onApply,
  onClear,
  onClickSortBy,
  searchValue,
  selectedStatus,
  selectedType,
  activeAccordionId,
  handleToggle,
  resolvedTheme,
  router,
  pathname,
  createMultiQueryString,
}: {
  cashbackHistoryData?: GetCbHistoryResponse;
  onApply: (options?: { selectedStatus?: string[]; selectedType?: string[] }) => void;
  onClear: () => void;
  onClickSortBy: MenuProps['onClick'];
  searchValue: string;
  selectedStatus: string[];
  selectedType: string[];
  activeAccordionId: number;
  handleToggle: (id: number) => void;
  resolvedTheme: string | undefined;
  router: any;
  pathname: string;
  createMultiQueryString: any;
}) => (
  <motion.div
    animate={{ opacity: 1, x: 0 }}
    className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full pb-[80px] px-2 md:px-4 lg:px-8'
    initial={{ opacity: 0, x: 30 }}
    transition={{ duration: 0.5, delay: 0.4 }}
  >
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      initial={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.5, delay: 0.5 }}
    >
      <MyEarningsToolbar
        onApply={onApply}
        onClear={onClear}
        onClickSortBy={onClickSortBy}
        searchKey={searchValue}
        selectedStatusArray={selectedStatus}
        selectedTypeArray={selectedType}
      />
    </motion.div>
    <motion.div
      animate={{ opacity: 1 }}
      className='pt-[10px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <AnimatePresence>
        {cashbackHistoryData?.cbItems.length ? (
          cashbackHistoryData.cbItems.map((item, index) => (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={index}
              transition={{ duration: 0.3, delay: 0.7 + index * 0.05 }}
            >
              <CbHistoryAccordian
                activeId={activeAccordionId}
                data={{
                  id: index + 1,
                  orderDate: item.orderDate,
                  storeImgUrl: item.storeLogo,
                  cashbackAmount: item.cashbackAmount,
                  orderAmount: item.orderAmount,
                  remarks: item.remarks,
                  RefId: item.referenceId,
                  status: item.status,
                  earningsType: item.earningsType,
                  isShareAndEarn: item.isShareAndEarn,
                }}
                key={index}
                onClick={handleToggle}
              />
            </motion.div>
          ))
        ) : (
          <motion.div
            animate={{ opacity: 1 }}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
          </motion.div>
        )}
      </AnimatePresence>

      {cashbackHistoryData && cashbackHistoryData?.pagination?.pageSize > 0 && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex-center w-full my-[50px]'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <ConfigProvider
            theme={{
              algorithm:
                resolvedTheme === 'dark'
                  ? theme.darkAlgorithm
                  : theme.defaultAlgorithm,
            }}
          >
            <Pagination
              defaultCurrent={1}
              defaultPageSize={15}
              onChange={(pageNumber, pageSize) =>
                router.replace(
                  pathname +
                    '?' +
                    createMultiQueryString([
                      { name: 'page', value: pageNumber.toString() },
                      { name: 'pageSize', value: pageSize.toString() },
                    ])
                )
              }
              responsive
              total={cashbackHistoryData.pagination.total}
            />
          </ConfigProvider>
        </motion.div>
      )}
    </motion.div>
  </motion.div>
);

const IndexClientsMyEarnings = ({
  overviewData,
  cashbackHistoryData,
  tab,
}: {
  overviewData: UserOverviewResponse;
  cashbackHistoryData?: GetCbHistoryResponse;
  tab: string | undefined;
}) => {
  const router = useRouter();
  const [activeTabId, setActiveTabId] = useState(
    tab === 'cashback-history' ? 2 : 1
  );
  const [activeAccordionId, setActiveAccordionId] = useState(1);
  const dispatch = useAppDispatch();
  const { searchValue } = useAppSelector((state) => state.commonFilters);
  const { resolvedTheme } = useTheme();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);

  useEffect(() => {
    if (activeTabId === 2) {
      dispatch(setTitle('My Earnings'));
      const searchParam = searchParams.get('searchParam');
      if (searchParam) {
        dispatch(setSearchValue(searchParam));
      }
      setSelectedStatus(searchParams.get('status')?.split(',') || []);
      setSelectedType(searchParams.get('type')?.split(',') || []);

      dispatch(setSortItems(sortItems));
      dispatch(setStatusList(statusList));
      dispatch(setShowStoresFilter(false));
      dispatch(setHideSearchFilter(false));
      dispatch(setHideSortFilter(false));
      dispatch(setSingleDatePicker(false));
    }
  }, [dispatch, searchParams, activeTabId]);

  useEffect(() => {
    let filterCount = 0;
    if (selectedStatus.length > 0) {
      filterCount += selectedStatus.length;
    }
    if (selectedType.length > 0) {
      filterCount += selectedType.length;
    }
    dispatch(setTotalFiltersApplied(filterCount));
  }, [dispatch, selectedStatus, selectedType]);

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    router.replace(pathname + '?' + createQueryString('sortType', key));
  };

  const onApply = async (options?: { selectedStatus?: string[]; selectedType?: string[] }) => {
    const { selectedStatus = [], selectedType = [] } = options ?? {};
    const hasSelectedStatus = selectedStatus.length > 0;
    const hasSelectedType = selectedType.length > 0;

    if (!hasSelectedStatus && !hasSelectedType) {
      setSelectedStatus([]);
      setSelectedType([]);
      router.replace(
        pathname + '?' + createMultiQueryString([
          { name: 'status', value: '' },
          { name: 'type', value: '' }
        ])
      );
      return;
    }

    const selectedStatusString = selectedStatus.join(',');
    const selectedTypeString = selectedType.join(',');
    setSelectedStatus(selectedStatus);
    setSelectedType(selectedType);

    const queries = [
      { name: 'status', value: selectedStatusString },
      { name: 'type', value: selectedTypeString }
    ];
    const queryString = createMultiQueryString(queries);
    router.replace(pathname + '?' + queryString);
  };

  const onClear = () => {
    setSelectedStatus([]);
    setSelectedType([]);
    const queries = [
      { name: 'status', value: '' },
      { name: 'type', value: '' }
    ];
    const queryString = createMultiQueryString(queries);
    router.replace(pathname + '?' + queryString);
  };

  const handleToggle = (id: number) => {
    if (activeAccordionId === id) {
      setActiveAccordionId(0);
    } else {
      setActiveAccordionId(id);
    }
  };

  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'My Earnings', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={1} />
          <div className='bg-[#E0E0E0] dark:bg-[#1F222A] w-[calc(100vw-73px)] lg:w-[calc(100vw-271px)]'>
            {/* Tab Navigation */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='max-w-[100vw] z-20 lg:max-w-[calc(100vw-294px)] overflow-auto scrollbarNone lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32] shadow-md sticky top-[64px] lg:top-[103px]'
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
            >
              <TabsContainer
                activeId={activeTabId}
                items={[
                  { id: 1, label: 'Earnings Overview' },
                  { id: 2, label: 'Cashback History' },
                ]}
                setActiveId={setActiveTabId}
              />
            </motion.div>

            {/* Tab Content */}
            <AnimatePresence mode='wait'>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                initial={{ opacity: 0, y: 10 }}
                key={activeTabId}
                transition={{ duration: 0.4, ease: 'easeInOut' }}
              >
                {activeTabId === 1 && (
                  <OverviewTabContent
                    onCashbackEarnedClick={() => setActiveTabId(2)}
                    overviewData={overviewData}
                    router={router}
                  />
                )}
                {activeTabId === 2 && (
                  <CashbackHistoryTabContent
                    activeAccordionId={activeAccordionId}
                    cashbackHistoryData={cashbackHistoryData}
                    createMultiQueryString={createMultiQueryString}
                    handleToggle={handleToggle}
                    onApply={onApply}
                    onClear={onClear}
                    onClickSortBy={onClickSortBy}
                    pathname={pathname}
                    resolvedTheme={resolvedTheme}
                    router={router}
                    searchValue={searchValue}
                    selectedStatus={selectedStatus}
                    selectedType={selectedType}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsMyEarnings;
