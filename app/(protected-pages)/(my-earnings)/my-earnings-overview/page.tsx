import React from 'react';
import IndexClientsMyEarnings from './index-clients';
import fetchWrapper from '@/utils/fetch-wrapper';
import { cookies } from 'next/headers';
import { BASE_URL } from '@/config';
import type {
  UserOverviewResponse,
  GetCbHistoryResponse,
  UserControllerGetCashbackHistoryParams,
} from '@/services/api/data-contracts';

interface customSearchParams extends UserControllerGetCashbackHistoryParams {
  page: number;
  pageSize: number;
}

const getEarningsOverview = () => {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<UserOverviewResponse>(`${BASE_URL}/users/overview`, {
    token: token?.value,
  });
};

const getCashbackHistory = async (searchParams: customSearchParams) => {
  const {
    searchParam,
    sortType = 'newest',
    status,
    stores,
    page,
    pageSize,
    startDate,
    endDate,
    type,
  } = searchParams;

  const queryParams = Object.entries({
    searchParam,
    sortType,
    status,
    stores,
    page,
    pageSize,
    startDate,
    endDate,
    type,
  })
    .filter(([_, value]) => value) //eslint-disable-line
    .map(([key, value]) => `${key}=${value}`)
    .join('&');

  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<GetCbHistoryResponse>(
    `${BASE_URL}/users/cashback-history?${queryParams}`,
    {
      token: token?.value,
    }
  );
};

const Page = async ({ searchParams }: { searchParams: any }) => {
  let overviewData: UserOverviewResponse;
  let cashbackHistoryData: GetCbHistoryResponse | undefined;
  const tab = searchParams.tab;

  try {
    // Fetch overview data (always needed)
    overviewData = await getEarningsOverview();

    // Fetch cashback history data (for the second tab)
    try {
      cashbackHistoryData = await getCashbackHistory(searchParams);
      console.log(cashbackHistoryData); 
    } catch (error) {
      // Cashback history is optional, so we can continue without it
      console.warn('Failed to fetch cashback history:', error);
      cashbackHistoryData = undefined;
    }
  } catch (error) {
    return error;
  }

  return (
    <IndexClientsMyEarnings
      cashbackHistoryData={cashbackHistoryData}
      overviewData={overviewData}
      tab={tab}
    />
  );
};

export default Page;
