'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import ReferralHistoryCard from '@/app/components/my-earnings/referral-history-card';
import NoData from '@/app/components/no-data';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setSelectedStoreStatusFilter } from '@/redux/slices/common-filters-slice';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setShowStoresFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/global-search-slice';
import {
  type GetUsersByReferralCodeResponse,
  StatusEnum,
} from '@/services/api/data-contracts';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { ConfigProvider, type MenuProps, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import React, { useEffect, useState } from 'react';

const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Oldest',
    key: 'oldest',
  },
  {
    label: 'High to Low',
    key: 'highToLow',
  },
  {
    label: 'Low to High',
    key: 'lowToHigh',
  },
  {
    label: 'No of Orders',
    key: 'noOfOrders',
  },
];

const statusList: StatusEnum[] = [
  StatusEnum.Active,
  StatusEnum.Inactive,
  StatusEnum.Blocked,
];

const IndexClientsReferralHistory = ({
  data,
}: {
  data: GetUsersByReferralCodeResponse;
}) => {
  const dispatch = useAppDispatch();
  const { clickStatus, searchValue } = useAppSelector(
    (state) => state.commonFilters
  );
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const pathname = usePathname();
  const searchParams = useSearchParams();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  useEffect(() => {
    dispatch(setTitle('Referral History'));
    const searchParam = searchParams.get('searchParam');
    if (searchParam) {
      dispatch(setSearchValue(searchParam));
    }
    setSelectedStatus(searchParams.get('status')?.split(',') ?? []);
  }, [dispatch, searchParams]);

  useEffect(() => {
    dispatch(setSortItems(sortItems));
    dispatch(setStatusList(statusList));
    dispatch(setShowStoresFilter(false));
    dispatch(setHideSearchFilter(false));
    dispatch(setHideSortFilter(false));
    dispatch(setSingleDatePicker(false));
  }, [dispatch]);

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    replace(`${pathname}?${createQueryString('sortType', key)}`);
  };

  useEffect(() => {
    let filterCount = 0;
    if (clickStatus.length > 0) {
      filterCount += clickStatus.length;
    }
    dispatch(setTotalFiltersApplied(filterCount));
  }, [clickStatus, dispatch]);

  const onApply = async (options?: { selectedStatus?: string[] }) => {
    const { selectedStatus } = options ?? {};

    if (selectedStatus && selectedStatus.length > 0) {
      const selectedStatusString = selectedStatus.join(',');
      dispatch(setSelectedStoreStatusFilter(selectedStatus));

      const queries = [{ name: 'status', value: selectedStatusString }];
      const queryString = createMultiQueryString(queries);
      replace(`${pathname}?${queryString}`);
    } else {
      const queryString = createQueryString('status', '');
      replace(`${pathname}?${queryString}`);
    }
  };

  const onClear = () => {
    setSelectedStatus([]);
    const queries = [{ name: 'status', value: '' }];
    const queryString = createMultiQueryString(queries);
    replace(`${pathname}?${queryString}`);
  };

  return (
    <>
      <CommonHeader
        headline='My Earnings'
        subHeading={<span>Referral History</span>}
      />

      <section
        className='header-container'
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className='sticky top-[104px] z-[30]'>
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'Payment History', link: '/' },
            ]}
          />
        </div>
        <div className='w-full flex z-[0] relative'>
          <MyEarningsSidenav activeNavId={5} />
          <div className='bg-[#E0E0E0] dark:bg-[#1F222A] w-[calc(100vw-73px)] lg:w-[calc(100vw-271px)] pb-[80px]'>
            <div>
              <MyEarningsToolbar
                onApply={onApply}
                onClear={onClear}
                onClickSortBy={onClickSortBy}
                searchKey={searchValue}
                selectedStatusArray={selectedStatus}
              />
            </div>

            <div className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex items-center flex-col gap-y-[10px]'>
              {data?.users.length ? (
                data?.users?.map((item) => (
                  <div className='w-full' key={item.uid}>
                    <ReferralHistoryCard item={item} />
                  </div>
                ))
              ) : (
                <div className='w-full'>
                  <NoData />
                </div>
              )}

              {data && data?.pagination?.pageSize > 0 && (
                <div className='flex-center w-full my-[50px]'>
                  <ConfigProvider
                    theme={{
                      algorithm:
                        resolvedTheme === 'dark'
                          ? theme.darkAlgorithm
                          : theme.defaultAlgorithm,
                    }}
                  >
                    <Pagination
                      defaultCurrent={1}
                      defaultPageSize={15}
                      onChange={(pageNumber, pageSize) =>
                        replace(
                          `${pathname}?${createMultiQueryString([
                            { name: 'page', value: pageNumber.toString() },
                            { name: 'pageSize', value: pageSize.toString() },
                          ])}`
                        )
                      }
                      total={data.pagination.total}
                    />
                  </ConfigProvider>
                </div>
              )}
            </div>
          </div>
        </div>
      </section>
    </>
  );
};

export default IndexClientsReferralHistory;
