'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import { InputFieldNormal } from '@/app/components/atoms/form-inputs';
import ThemeButton from '@/app/components/atoms/theme-btn';
import CommonHeader from '@/app/components/headers/common-header';
import LinkContainerWithoutCaption from '@/app/components/my-earnings/link-container';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import CashbackHandSVG from '@/app/components/svg/cashback-hand';
import FaqSVG from '@/app/components/svg/faq';
import Support24SVG from '@/app/components/svg/support24';
import Image from 'next/image';
import redeemGiftcardImg from '@/public/img/my-earnings/giftcard-redeem.png';
import { useAppSelector } from '@/redux/hooks';
import { useRouter } from 'next/navigation';
import { motion } from 'framer-motion';

const IndexClientsRedeemIcbGiftcard = () => {
  const router = useRouter();
  const { userDetails } = useAppSelector((state) => state.auth);
  return (
    <>
      <CommonHeader
        headline='My Earnings'
        subHeading={<span>Redeem ICB Card</span>}
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'Payment History', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex z-[0] relative'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={10} />
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full pb-[80px] px-[6px]'
            initial={{ opacity: 0, x: 20 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='mx-auto w-fit'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.4 }}
              whileHover={{ scale: 1.05, rotate: 2 }}
              whileTap={{ scale: 0.95 }}
            >
              <Image
                alt='redeem giftcard'
                className='w-auto h-[142px] mx-auto mt-[38px] lg:mt-[30px]'
                src={redeemGiftcardImg}
                title='redeem giftcard'
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='flex-center lg:items-start flex-col lg:flex-row rounded-[5px] bg-container mt-[18px] pt-[16px] pb-[24px] px-[8px] lg:mx-[30px] lg:py-[30px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.5 }}
              whileHover={{ boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)' }}
            >
              <motion.div
                animate={{ opacity: 1, scale: 1 }}
                className='lg:mt-[50px]'
                initial={{ opacity: 0, scale: 0.8 }}
                transition={{ duration: 0.5, delay: 0.6 }}
              >
                <motion.h4
                  animate={{ opacity: 1 }}
                  className='text-[8px] lg:text-xs font-medium text-center'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.7 }}
                >
                  Current Balance
                </motion.h4>
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='w-[110px] h-[40px] mt-[6px] bg-[#FFDC97] rounded-[5px] text-black text-[19px] font-black flex-center'
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                  whileHover={{
                    scale: 1.1,
                    boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
                  }}
                >
                  ₹ {userDetails.balance}
                </motion.div>
              </motion.div>
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className='w-[250px] sm:w-[267px] lg:w-[400px] mx-auto lg:ml-[50px] lg:mr-0'
                initial={{ opacity: 0, x: 20 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='mt-[30px] lg:mt-0'
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.3, delay: 0.8 }}
                >
                  <motion.h4
                    animate={{ opacity: 1 }}
                    className='text-[8px] lg:text-xs pl-[8px] font-medium lg: text-blackWhite'
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.3, delay: 0.9 }}
                  >
                    Giftcard Number
                    <motion.span
                      animate={{ scale: 1 }}
                      className='sup text-[#FF4141] font-bold'
                      initial={{ scale: 0 }}
                      transition={{ duration: 0.3, delay: 1 }}
                    >
                      *
                    </motion.span>
                  </motion.h4>
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.3, delay: 1 }}
                    whileHover={{
                      scale: 1.02,
                      boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <InputFieldNormal
                      placeholder='Enter Giftcard Number'
                      rootClass='mt-[4px]'
                    />
                  </motion.div>
                </motion.div>
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  className='mt-[22px]'
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.3, delay: 1.1 }}
                >
                  <motion.h4
                    animate={{ opacity: 1 }}
                    className='text-[8px] lg:text-xs pl-[8px] font-medium text-blackWhite'
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.3, delay: 1.2 }}
                  >
                    Giftcard PIN
                    <motion.span
                      animate={{ scale: 1 }}
                      className='sup text-[#FF4141] bold'
                      initial={{ scale: 0 }}
                      transition={{ duration: 0.3, delay: 1.3 }}
                    >
                      *
                    </motion.span>
                  </motion.h4>
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    initial={{ opacity: 0, y: 10 }}
                    transition={{ duration: 0.3, delay: 1.3 }}
                    whileHover={{
                      scale: 1.02,
                      boxShadow: '0px 4px 15px rgba(0, 0, 0, 0.1)',
                    }}
                  >
                    <InputFieldNormal
                      placeholder='Enter Giftcard PIN'
                      rootClass='mt-[4px]'
                    />
                  </motion.div>
                </motion.div>
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 10 }}
                  transition={{ duration: 0.3, delay: 1.4 }}
                  whileHover={{ scale: 1.05 }}
                >
                  <ThemeButton
                    className='!w-[95px] uppercase mx-auto mt-[30px]'
                    onClick={() => console.log('')}
                    text='Redeem'
                  />
                </motion.div>
              </motion.div>
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='grid grid-cols-1 lg:grid-cols-2 lg:justify-items-center gap-[13px] mt-[30px] lg:mx-[30px]'
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 1.5 }}
            >
              <LinkContainerWithoutCaption
                icon={
                  <FaqSVG className='w-[14px] lg:w-[20px] text-[#818181]' />
                }
                onClick={() => router.push('/faqs')}
                title='Related Question and Answers'
              />
              <LinkContainerWithoutCaption
                icon={
                  <Support24SVG className='w-[11px] lg:w-[20px] text-[#818181]' />
                }
                onClick={() => {
                  router.push('https://tawk.to/indiancashback/');
                }}
                title='Support'
              />
              <LinkContainerWithoutCaption
                icon={
                  <CashbackHandSVG className='w-[13px] lg:w-[20px] text-[#818181]' />
                }
                onClick={() => {
                  router.push('/');
                }}
                title='Cashback Home'
              />
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsRedeemIcbGiftcard;
