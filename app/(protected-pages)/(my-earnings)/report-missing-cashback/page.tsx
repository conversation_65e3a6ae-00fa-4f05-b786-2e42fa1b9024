import React from 'react';
import IndexClientsReportMissingCashback from './index-clients';
import type { CustomSearchParamsTypes } from '@/types/global-types';
import { BASE_URL } from '@/config';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type {
  ClicksResponse,
  MissingCashbackControllerListMissingCashbackParams,
  MissingCashbackResponse,
} from '@/services/api/data-contracts';
import type { ClickedStores } from '../click-history/page';

interface customSearchParams
  extends MissingCashbackControllerListMissingCashbackParams {
  page: number;
  pageSize: number;
}

const getCashbackHistory = async (searchParams: customSearchParams) => {
  const {
    searchParam = '',
    sortType = 'newest',
    status = '',
    stores = '',
    page = '1',
    pageSize = '15',
    startDate = '',
    endDate = '',
  } = searchParams;

  let queryParams = `page=${page}&pageSize=${pageSize}&sortType=${sortType}&searchParam=${searchParam}`;
  if (status !== '') {
    queryParams += `&status=${status}`;
  }
  if (startDate && endDate) {
    queryParams += `&startDate=${startDate}&endDate=${endDate}`;
  }
  if (stores !== '') {
    queryParams += `&stores=${stores}`;
  }
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return fetchWrapper<MissingCashbackResponse>(
    `${BASE_URL}/click/missing-cashback/list?${queryParams}`,
    {
      token: token?.value,
    }
  );
};

async function getClickByStore(searchParams: CustomSearchParamsTypes) {
  const {
    // searchParam = '',
    sortType = 'newest',
    stores = '',
    status = '',
    // userType = 'both',
    // offerType = 'deals',
    page = '1',
    pageSize = '15',
    date = '',
    // startDate = '',
    // endDate = '',
  } = searchParams;

  let queryParams = `page=${page}&pageSize=${pageSize}&sortType=${sortType}`;
  // &searchParam=${searchParam}&startDate=${startDate}&endDate=${endDate}`;
  if (stores !== '') {
    queryParams += `&stores=${stores}`;
  }
  if (status !== '') {
    queryParams += `&status=${status}`;
  }
  if (date !== '') {
    queryParams += `&date=${date}`;
  }
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<ClicksResponse>(
    `${BASE_URL}/click/stores?${queryParams}`,
    {
      token: token?.value,
      cache: 'no-store',
    }
  );
}

async function getClickedStores() {
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken');
  return await fetchWrapper<ClickedStores[]>(
    `${BASE_URL}/click/clicked_stores`,
    {
      token: token?.value,
    }
  );
}

const Page = async ({
  searchParams,
}: {
  searchParams: CustomSearchParamsTypes;
}) => {
  let resData: any;
  let clickedStores: ClickedStores[];
  let cashbackHistoryData: MissingCashbackResponse;
  try {
    resData = await getClickByStore(searchParams);
    clickedStores = await getClickedStores();
    cashbackHistoryData = await getCashbackHistory(searchParams as any);
  } catch (err: any) {
    console.log({ err });
    return (
      <div className="error-container">
        <h1>Error loading page</h1>
        <p>Please try again later.</p>
      </div>
    );
  }

  return (
    <IndexClientsReportMissingCashback
      cashbackHistoryData={cashbackHistoryData}
      clickedStores={clickedStores}
      data={resData}
    />
  );
};

export default Page;
export const dynamic = 'force-dynamic';
