import LinkContainerWithoutCaption from '@/app/components/my-earnings/link-container';
import CashbackHandSVG from '@/app/components/svg/cashback-hand';
import CopySVG from '@/app/components/svg/copy';
import FaqSVG from '@/app/components/svg/faq';
import RightArrow from '@/app/components/svg/right-arrow';
import Support24SVG from '@/app/components/svg/support24';
import TicketSVG from '@/app/components/svg/ticket';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setReportMissingStep } from '@/redux/slices/report-missing-cb-slice';
import { copyToClipboard } from '@/utils/helpers';
import Image from 'next/image';
import { useRouter } from 'next/navigation';
import React from 'react';
import { toast } from 'react-toastify';

interface RmcStep4Props {
  onBackToWelcome?: () => void;
  changeTab?: (tab: string) => void;
}

const RmcStep4 = ({ onBackToWelcome, changeTab }: RmcStep4Props) => {
  const { reportMissingStep, complaintId } = useAppSelector(
    (state) => state.reportMissingCbStep
  );
  const dispatch = useAppDispatch();
  const router = useRouter();

  const copyUrl = () => {
    copyToClipboard(complaintId);
    toast.success('Complaint ID Copied to Clipboard');
  };

  if (reportMissingStep !== 4) {
    return;
  }
  return (
    <div>
      <div className='lg:w-[50%] mx-auto'>
        <div className='lg:flex lg:mt-[70px]'>
          <div className='mx-auto'>
            <Image
              alt='illustration'
              className='w-[80px] lg:w-[100px] mx-auto'
              height={256}
              src='/img/happy-boy.png'
              title='illustration'
              width={185}
            />
          </div>
          <div className='flex flex-col items-center mt-[10px] lg:mt-0 grow'>
            <h4 className='text-sm font-bold'>You are Done!</h4>
            <span className='mt-[5px] text-[10px] lg:text-xs font-medium text-center max-w-[175px] lg:max-w-[220px]'>
              We have received your complaint. We are trying to resolve it ASAP!
            </span>
            <button
              className='flex-center relative w-[90%] mt-[12px] h-[45px] bg-[#A5E0AB] rounded-[5px] text-[10px] lg:text-xs font-medium lg:mt-[25px] text-black'
              onClick={() => copyUrl()}
              type='button'
            >
              <span>Complaint ID :</span>
              <div className='flex gap-x-[4px]'>
                <span className='font-extrabold ml-[8px]'>{complaintId}</span>
                <CopySVG className='w-[18px] text-black ' />
              </div>
            </button>
            <div className='flex flex-col lg:flex-row gap-[12px] lg:gap-[20px] mt-[16px]'>
              <button
                className='text-primary dark:text-white text-xs lg:text-sm font-medium lg:font-semibold flex-center'
                onClick={() => dispatch(setReportMissingStep(1))}
                type='button'
              >
                <RightArrow className='text-primary dark:text-white w-[10px] mr-[10px] rotate-180' />
                Report Again
              </button>
              {onBackToWelcome && (
                <button
                  className='text-gray-600 dark:text-gray-400 text-xs lg:text-sm font-medium lg:font-semibold flex-center'
                  onClick={onBackToWelcome}
                  type='button'
                >
                  <RightArrow className='text-gray-600 dark:text-gray-400 w-[10px] mr-[10px] rotate-180' />
                  Back to Home
                </button>
              )}
            </div>
          </div>
        </div>
      </div>
      <div className='grid grid-cols-1 lg:grid-cols-2 gap-[14px] mt-[45px] mb-[50px] items-center mx-[8px] lg:w-[80%] xl:w-[60%] lg:mx-auto'>
        <LinkContainerWithoutCaption
          icon={
            <TicketSVG className='w-[15px] lg:w-[20px] text-[#818181] dark:text-white' />
          }
          onClick={() => {
            changeTab?.('missing-history');
          }}
          title='Check Ticket Status'
        />
        <LinkContainerWithoutCaption
          icon={
            <FaqSVG className='w-[14px] lg:w-[24px] text-[#818181] dark:text-white' />
          }
          onClick={() => {
            router.push('https://tawk.to/indiancashback');
          }}
          title='Related Question and Answers'
        />
        <LinkContainerWithoutCaption
          icon={
            <Support24SVG className='w-[11px] lg:w-[20px] text-[#818181] dark:text-white' />
          }
          title='Support'
        />
        <LinkContainerWithoutCaption
          icon={
            <CashbackHandSVG className='w-[13px] lg:w-[20px] text-[#818181] dark:text-white' />
          }
          title='Cashback Home'
        />
      </div>
    </div>
  );
};

export default RmcStep4;
