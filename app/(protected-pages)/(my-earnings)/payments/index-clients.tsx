'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import MyEarningsSidenav from '@/app/components/my-earnings/my-earnings-sidenav';
import MyEarningsToolbar from '@/app/components/my-earnings/my-earnings-toolbar';
import PaymentHistoryCont from '@/app/components/my-earnings/payment-history-cont';
import TabsContainer from '@/app/components/atoms/tabs-container';
import NoData from '@/app/components/no-data';
import React, { useState, useEffect } from 'react';
import PaymentRedeemCard from '@/app/components/my-earnings/payment-redeem-card';
import BottomDrawer from '@/app/components/atoms/BottomDrawer';
import BankUpiRedeemForm from './bank-upi-redeem-form';
import LinkContainerWithoutCaption from '@/app/components/my-earnings/link-container';
import FaqSVG from '@/app/components/svg/faq';
import Support24SVG from '@/app/components/svg/support24';
import CashbackHandSVG from '@/app/components/svg/cashback-hand';
import BankSVG from '@/app/components/svg/bank-icon';
import FAQAccordion from '@/app/components/accordians/faq-accordian';
import SearchInput from '@/app/components/atoms/search-input';
import { paymentFaqData } from './payment-faq-data';
import { Modal, ConfigProvider, type MenuProps, Pagination, theme } from 'antd';
import CrossSVG from '@/app/components/svg/cross';
import { useRouter, useSearchParams, usePathname } from 'next/navigation';
import type {
  GetBankAccountDataResponse,
  GetPaymentListResponse,
} from '@/services/api/data-contracts';
import { PaymentTypeStatusEnum } from '@/services/api/data-contracts';
import { useWindowSize } from 'usehooks-ts';
import { motion, AnimatePresence } from 'framer-motion';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setHideSearchFilter,
  setHideSortFilter,
  setShowStoresFilter,
  setSingleDatePicker,
  setSortItems,
  setStatusList,
  setTitle,
  setTotalFiltersApplied,
} from '@/redux/slices/earnings-toolbar-slice';
import { setSearchValue } from '@/redux/slices/global-search-slice';
import {
  useCreateMultiQueryString,
  useCreateQueryString,
} from '@/utils/custom-hooks';
import { useTheme } from 'next-themes';

// Sort items for payment history
const sortItems = [
  {
    label: 'Newest',
    key: 'newest',
  },
  {
    label: 'Oldest',
    key: 'oldest',
  },
  {
    label: 'Amount',
    key: 'amount',
  },
];

const statusList: PaymentTypeStatusEnum[] = [
  PaymentTypeStatusEnum.Paid,
  PaymentTypeStatusEnum.Requested,
  PaymentTypeStatusEnum.Cancelled,
];

// Payments Tab Content
const PaymentsTabContent = ({
  bankData,
  showWithdrawModal,
  setShowWithdrawModal,
  router,
  width,
}: {
  bankData: GetBankAccountDataResponse;
  showWithdrawModal: boolean;
  setShowWithdrawModal: (show: boolean) => void;
  router: any;
  width: number;
}) => {
  const { userDetails } = useAppSelector((state) => state.auth);
  const [activeAccordionId, setActiveAccordionId] = useState<number | null>(
    null
  );
  const [searchValue, setSearchValue] = useState('');
  const [filteredFAQs, setFilteredFAQs] = useState(paymentFaqData);

  // Handle accordion toggle
  const handleToggle = (id: number) => {
    setActiveAccordionId(activeAccordionId === id ? null : id);
  };

  // Handle search input change
  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    if (value.trim()) {
      const searchTerm = value.toLowerCase();
      const filtered = paymentFaqData.filter(
        (faq) =>
          faq.question.toLowerCase().includes(searchTerm) ||
          faq.answer.toLowerCase().includes(searchTerm)
      );
      setFilteredFAQs(filtered);
    } else {
      setFilteredFAQs(paymentFaqData);
    }
  };
  return (
    <motion.div
      animate={{ opacity: 1, x: 0 }}
      className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full pb-[80px] px-2 md:px-4 lg:px-8'
      initial={{ opacity: 0, x: 20 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <div className='mt-[15px] lg:mt-[27px] bg-container rounded-[10px] shadow-sm pt-[14px] px-[8px] pb-[30px] lg:pt-[23px] lg:px-[20px] lg:mx-[20px] xl:mx-[50px] text-blackWhite'>
        <div className='flex flex-col gap-y-[10px] justify-center items-center mb-[20px]'>
          <motion.h4
            animate={{ opacity: 1, x: 0 }}
            className='text-xs lg:text-sm font-pat'
            initial={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            Withdrawable Amount
          </motion.h4>
          <motion.h3
            animate={{ opacity: 1, x: 0 }}
            className='text-xl font-pat my-2'
            initial={{ opacity: 0, x: -10 }}
            transition={{ duration: 0.5, delay: 0.6 }}
          >
            ₹ {userDetails?.balance}
          </motion.h3>
        </div>
        <motion.h4
          animate={{ opacity: 1, x: 0 }}
          className='text-xs lg:text-sm font-pat'
          initial={{ opacity: 0, x: -10 }}
          transition={{ duration: 0.5, delay: 0.6 }}
        >
          Select Where To Redeem ?
        </motion.h4>
        <div className='mt-[22px] grid grid-cols-2 gap-x-[8px] xl:gap-x-[20px] gap-y-[12px]'>
          <PaymentRedeemCard
            caption='Transfer to Bank'
            imgUrl='/img/my-earnings/paymentIcbCard.png'
            onClick={() => setShowWithdrawModal(true)}
            title='Bank Transfer'
          />
          <PaymentRedeemCard
            caption='Coming soon!!'
            imgUrl='/img/my-earnings/paymentBank.png'
            offer={{ percent: 10 }}
            title='ICB Card'
          />
        </div>
      </div>

      {/* Links */}
      <div className='flex flex-col gap-[13px] md:grid md:grid-cols-2 lg:justify-items-center mt-[20px] lg:px-[20px] xl:px-[50px]'>
        <LinkContainerWithoutCaption
          icon={<BankSVG className='w-[12px] lg:w-[18px] text-[#818181]' />}
          onClick={() => router.push('/bank-details')}
          title='Update Bank'
        />
        <LinkContainerWithoutCaption
          icon={<FaqSVG className='w-[14px] lg:w-[24px] text-[#818181]' />}
          onClick={() => router.push('/faqs')}
          title='Related Question and Answers'
        />
        <LinkContainerWithoutCaption
          icon={
            <Support24SVG className='w-[11px] lg:w-[20px] text-[#818181]' />
          }
          onClick={() => router.push('https://tawk.to/indiancashback/')}
          title='Support'
        />
        <LinkContainerWithoutCaption
          icon={
            <CashbackHandSVG className='w-[13px] lg:w-[21px] text-[#818181]' />
          }
          onClick={() => router.push('/')}
          title='Cashback Home'
        />
      </div>

      {/* FAQ Section */}
      <motion.h4
        animate={{ opacity: 1, x: 0 }}
        className='text-xs lg:text-sm font-pat mt-10 mb-2 text-center'
        initial={{ opacity: 0, x: -10 }}
        transition={{ duration: 0.5, delay: 0.6 }}
      >
        Frequently Asked Questions
      </motion.h4>
      <motion.div
        animate={{ opacity: 1, y: 0 }}
        className='mt-6 bg-container rounded-[10px] shadow-sm pt-[20px] px-[8px] pb-[30px] lg:pt-[30px] lg:px-[20px] lg:mx-[20px] xl:mx-[50px] text-blackWhite'
        initial={{ opacity: 0, y: 20 }}
        transition={{ duration: 0.5, delay: 0.8 }}
      >
        {/* Search Bar */}
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='w-full max-w-xl mb-[15px] lg:mb-[20px] mx-auto'
          initial={{ opacity: 0, y: 10 }}
          transition={{ duration: 0.5, delay: 1.0 }}
        >
          <SearchInput
            onChange={handleSearchChange}
            onClose={() => {
              setSearchValue('');
              setFilteredFAQs(paymentFaqData);
            }}
            placeholder='Search payment questions...'
            rootClass='w-full mx-auto'
            value={searchValue}
          />
        </motion.div>

        {/* FAQ Content */}
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 1.1 }}
        >
          {filteredFAQs.length === 0 ? (
            <motion.div
              animate={{ opacity: 1 }}
              className='text-center py-[20px] lg:py-[30px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5 }}
            >
              <h3 className='text-sm lg:text-base font-medium text-gray-700 dark:text-gray-300 mb-2'>
                No results found
              </h3>
              <p className='text-xs lg:text-sm text-gray-500 dark:text-gray-400'>
                We couldn't find any FAQs matching your search. Try different
                keywords.
              </p>
            </motion.div>
          ) : (
            <div className='space-y-[10px]'>
              {filteredFAQs.map((faq, index) => (
                <motion.div
                  animate={{ opacity: 1, y: 0 }}
                  initial={{ opacity: 0, y: 15 }}
                  key={faq.id}
                  transition={{ duration: 0.4, delay: 1.2 + index * 0.1 }}
                >
                  <FAQAccordion
                    activeId={activeAccordionId}
                    answer={faq.answer}
                    highlight={searchValue}
                    id={faq.id}
                    onClick={handleToggle}
                    question={faq.question}
                  />
                </motion.div>
              ))}
            </div>
          )}
        </motion.div>
      </motion.div>

      {/* Modals */}
      {width <= 1023 ? (
        <BottomDrawer
          maskClosable={false}
          onClose={() => setShowWithdrawModal(false)}
          open={showWithdrawModal}
          sectionClass='!px-0'
          title=''
          titleIcon={''}
        >
          <BankUpiRedeemForm
            data={bankData}
            setShowWithdrawModal={setShowWithdrawModal}
          />
        </BottomDrawer>
      ) : (
        <Modal
          cancelText=''
          centered
          classNames={{
            content: '!bg-container',
            header: '!bg-container !text-blackWhite',
          }}
          closeIcon={
            <CrossSVG className='text-blackWhite opacity-50 w-[12px]' />
          }
          destroyOnClose={true}
          footer={<></>}
          maskClosable={false}
          okText=''
          onCancel={() => setShowWithdrawModal(false)}
          open={showWithdrawModal}
          title={
            <h4 className='text-sm font-pat text-blackWhite font-normal'>
              Select an Amount to Redeem
            </h4>
          }
        >
          <BankUpiRedeemForm
            data={bankData}
            setShowWithdrawModal={setShowWithdrawModal}
          />
        </Modal>
      )}
    </motion.div>
  );
};

// Payment History Tab Content
const PaymentHistoryTabContent = ({
  paymentHistoryData,
  onApply,
  onClear,
  onClickSortBy,
  searchValue,
  selectedStatus,
  resolvedTheme,
  router,
  pathname,
  createMultiQueryString,
}: {
  paymentHistoryData?: GetPaymentListResponse;
  onApply: (options?: { selectedStatus?: string[] }) => void;
  onClear: () => void;
  onClickSortBy: MenuProps['onClick'];
  searchValue: string;
  selectedStatus: string[];
  resolvedTheme: string | undefined;
  router: any;
  pathname: string;
  createMultiQueryString: any;
}) => (
  <motion.div
    animate={{ opacity: 1, x: 0 }}
    className='bg-[#E0E0E0] dark:bg-[#1F222A] w-full pb-[80px]'
    initial={{ opacity: 0, x: 30 }}
    transition={{ duration: 0.5, delay: 0.4 }}
  >
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      initial={{ opacity: 0, y: -10 }}
      transition={{ duration: 0.5, delay: 0.5 }}
    >
      <MyEarningsToolbar
        onApply={onApply}
        onClear={onClear}
        onClickSortBy={onClickSortBy}
        searchKey={searchValue}
        selectedStatusArray={selectedStatus}
        showTitle={false}
      />
    </motion.div>
    <motion.div
      animate={{ opacity: 1 }}
      className='pt-[12px] px-[6px] lg:px-[15px] xl:px-[30px] flex flex-col gap-y-[10px]'
      initial={{ opacity: 0 }}
      transition={{ duration: 0.5, delay: 0.6 }}
    >
      <AnimatePresence>
        {paymentHistoryData?.payments.length ? (
          paymentHistoryData.payments.map((item, index) => (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -20 }}
              initial={{ opacity: 0, y: 20 }}
              key={index}
              transition={{ duration: 0.3, delay: 0.7 + index * 0.05 }}
            >
              <PaymentHistoryCont
                data={{
                  paymentDate: item.paymentDate,
                  paymentType: item.paymentType,
                  referenceId: item.referenceId,
                  status: item.status,
                  withdrawAmount: item.withdrawAmount,
                }}
                key={index}
              />
            </motion.div>
          ))
        ) : (
          <motion.div
            animate={{ opacity: 1 }}
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.7 }}
          >
            <NoData imgClass='!w-[200px] !h-[200px] lg:!w-[300px] lg:!h-[300px]' />
          </motion.div>
        )}
      </AnimatePresence>

      {paymentHistoryData && paymentHistoryData?.pagination?.pageSize > 0 && (
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex-center w-full my-[50px]'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.8 }}
        >
          <ConfigProvider
            theme={{
              algorithm:
                resolvedTheme === 'dark'
                  ? theme.darkAlgorithm
                  : theme.defaultAlgorithm,
            }}
          >
            <Pagination
              defaultCurrent={1}
              defaultPageSize={15}
              onChange={(pageNumber, pageSize) =>
                router.replace(
                  pathname +
                    '?' +
                    createMultiQueryString([
                      { name: 'page', value: pageNumber.toString() },
                      { name: 'pageSize', value: pageSize.toString() },
                    ])
                )
              }
              responsive
              total={paymentHistoryData.pagination.total}
            />
          </ConfigProvider>
        </motion.div>
      )}
    </motion.div>
  </motion.div>
);

const IndexClientsPayments = ({
  bankData,
  paymentHistoryData,
  tab,
}: {
  bankData: GetBankAccountDataResponse;
  paymentHistoryData?: GetPaymentListResponse;
  tab: string | undefined;
}) => {
  const router = useRouter();
  const [activeTabId, setActiveTabId] = useState(
    tab === 'payment-history' ? 2 : 1
  );
  const [showWithdrawModal, setShowWithdrawModal] = useState(false);
  const { width = 0 } = useWindowSize();
  const dispatch = useAppDispatch();
  const { searchValue } = useAppSelector((state) => state.commonFilters);
  const { resolvedTheme } = useTheme();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const createQueryString = useCreateQueryString(searchParams);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);

  useEffect(() => {
    if (activeTabId === 2) {
      dispatch(setTitle('Payment History'));
      const searchParam = searchParams.get('searchParam');
      if (searchParam) {
        dispatch(setSearchValue(searchParam));
      }
      setSelectedStatus(searchParams.get('status')?.split(',') ?? []);

      dispatch(setSortItems(sortItems));
      dispatch(setStatusList(statusList));
      dispatch(setShowStoresFilter(false));
      dispatch(setHideSearchFilter(false));
      dispatch(setHideSortFilter(false));
      dispatch(setSingleDatePicker(false));
    }
  }, [dispatch, searchParams, activeTabId]);

  useEffect(() => {
    let filterCount = 0;
    if (selectedStatus.length > 0) {
      filterCount += selectedStatus.length;
    }
    dispatch(setTotalFiltersApplied(filterCount));
  }, [dispatch, selectedStatus]);

  const onClickSortBy: MenuProps['onClick'] = ({ key }) => {
    router.replace(pathname + '?' + createQueryString('sortType', key));
  };

  const onApply = async (options?: { selectedStatus?: string[] }) => {
    const { selectedStatus = [] } = options ?? {};
    const hasSelectedStatus = selectedStatus.length > 0;

    if (!hasSelectedStatus) {
      setSelectedStatus([]);
      router.replace(
        pathname + '?' + createMultiQueryString([{ name: 'status', value: '' }])
      );
      return;
    }

    const selectedStatusString = selectedStatus.join(',');
    setSelectedStatus(selectedStatus);

    const queries = [{ name: 'status', value: selectedStatusString }];
    const queryString = createMultiQueryString(queries);
    router.replace(pathname + '?' + queryString);
  };

  const onClear = () => {
    setSelectedStatus([]);
    const queries = [{ name: 'status', value: '' }];
    const queryString = createMultiQueryString(queries);
    router.replace(pathname + '?' + queryString);
  };

  return (
    <>
      <CommonHeader headline='My Earnings' subHeading={<span>Overview</span>} />

      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='sticky top-[104px] z-[30]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'User Page', link: '/my-profile' },
              { title: 'My Earnings', link: '/' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='w-full flex'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <MyEarningsSidenav activeNavId={2} />
          <div className='bg-[#E0E0E0] dark:bg-[#1F222A] w-[calc(100vw-73px)] lg:w-[calc(100vw-271px)]'>
            {/* Tab Navigation */}
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='max-w-[100vw] lg:max-w-[calc(100vw-294px)] overflow-auto scrollbarNone lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32] shadow-md z-[3] sticky top-[64px] lg:top-[103px]'
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.3, ease: 'easeOut' }}
            >
              <TabsContainer
                activeId={activeTabId}
                items={[
                  { id: 1, label: 'Payments' },
                  { id: 2, label: 'Payment History' },
                ]}
                setActiveId={setActiveTabId}
              />
            </motion.div>

            {/* Tab Content */}
            <AnimatePresence mode='wait'>
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -10 }}
                initial={{ opacity: 0, y: 10 }}
                key={activeTabId}
                transition={{ duration: 0.4, ease: 'easeInOut' }}
              >
                {activeTabId === 1 && (
                  <PaymentsTabContent
                    bankData={bankData}
                    router={router}
                    setShowWithdrawModal={setShowWithdrawModal}
                    showWithdrawModal={showWithdrawModal}
                    width={width}
                  />
                )}
                {activeTabId === 2 && (
                  <PaymentHistoryTabContent
                    createMultiQueryString={createMultiQueryString}
                    onApply={onApply}
                    onClear={onClear}
                    onClickSortBy={onClickSortBy}
                    pathname={pathname}
                    paymentHistoryData={paymentHistoryData}
                    resolvedTheme={resolvedTheme}
                    router={router}
                    searchValue={searchValue}
                    selectedStatus={selectedStatus}
                  />
                )}
              </motion.div>
            </AnimatePresence>
          </div>
        </motion.div>
      </motion.section>
    </>
  );
};

export default IndexClientsPayments;
