'use client';
import React, { useRef, useState } from 'react';
import CommonHeader from '@/app/components/headers/common-header';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import Image from 'next/image';
import { InputFieldWithIcon2 } from '@/app/components/atoms/form-inputs';
import PillButton from '@/app/components/atoms/pills';
import { Switch } from 'antd';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  setUserDetails,
  toggleUserPersonalInterest,
} from '@/redux/slices/auth-slice';
import { PersonalInterestTypes } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';
import CommonOtpModal from '@/app/components/modals/common-otp-modal';
import OTPInput from 'react-otp-input';
import ThemeButton from '@/app/components/atoms/theme-btn';
import { LoadingGif } from '@/app/components/misc/loading-components';
import clsx from 'clsx';
import { motion } from 'framer-motion';

interface updateCredentials {
  email?: string;
  mobile?: number;
  type: 'mobile' | 'email';
}

export const updateProfile = async ({ formData }: { formData: any }) => {
  const url = '/api/proxy/users/update-profile';
  return fetchWrapper(url, {
    body: formData,
    method: 'POST',
    deleteContentType: true,
  });
};

export const updateCredentials = async ({
  data,
}: {
  data: updateCredentials;
}) => {
  const url = '/api/proxy/users/update-credentials';
  return fetchWrapper(url, {
    body: JSON.stringify(data),
    method: 'POST',
  });
};

const allowedExtensions = ['png', 'jpg', 'jpeg', 'webp'];
const maxFileSizeMB = 2;

const IndexClientsProfileSetting = ({
  personalInterest,
}: {
  personalInterest: PersonalInterestTypes[];
}) => {
  const { userDetails } = useAppSelector((state) => state.auth);
  const dispatch = useAppDispatch();
  const fileInputRef = useRef<HTMLInputElement>(null);
  const [isShowOtpModal, setShowOtpModal] = useState(false);
  const [otp, setOtp] = useState<number>();
  const [uploadingImg, setUploadingImg] = useState(false);
  const [otpFor, setOtpFor] = useState<'email' | 'mobile'>('email');
  const [isLoading, setLoading] = useState(false);
  const [otpErr, setOtpErr] = useState('');
  const [imgErr, setImgErr] = useState('');

  const handleImageChange = async (event: any) => {
    try {
      const file = event.target.files[0];

      const fileExtension = file.name.split('.').pop().toLowerCase();

      if (!allowedExtensions.includes(fileExtension)) {
        return setImgErr(
          'File type not allowed. Only PNG, JPEG, JPG, and WEBP are allowed.'
        );
      } else if (file.size / (1024 * 1024) > maxFileSizeMB) {
        return setImgErr(
          `File size exceeds the maximum limit of ${maxFileSizeMB} MB.`
        );
      } else {
        setImgErr('');
        // File is valid, you can proceed with further actions, e.g., upload to server
      }

      const formData = new FormData();
      formData.append('profileImage', file);
      setUploadingImg(true);
      const res = await updateProfile({ formData });
      if (res) {
        const fileURL = URL.createObjectURL(file);
        dispatch(setUserDetails({ avatar: fileURL }));
      }
    } catch (error) {
      console.log(error);
    } finally {
      setUploadingImg(false);
    }
  };

  const handleName = async () => {
    if (userDetails?.name) {
      const formData = new FormData();
      formData.append(
        'profileData',
        JSON.stringify({
          name: userDetails.name,
        })
      );
      const res = await updateProfile({ formData });
      if (res) {
        toast.success('Successfully updated name');
      }
    } else {
      toast.error('Please enter your name');
    }
  };
  const handleEmail = async () => {
    if (
      userDetails?.email &&
      /^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$/g.test(userDetails.email)
    ) {
      await updateCredentials({
        data: { email: userDetails.email.trim(), type: 'email' },
      });
      setOtpFor('email');
      setShowOtpModal(true);
    } else {
      toast.error('Please enter correct email');
    }
  };
  const handleMobile = async () => {
    if (userDetails?.mobile && /^[0-9]{10}$/g.test(userDetails?.mobile)) {
      await updateCredentials({
        data: { mobile: Number(userDetails?.mobile.trim()), type: 'mobile' },
      });
      setOtpFor('mobile');
      setShowOtpModal(true);
    } else {
      toast.error('Please enter 10 digit mobile');
    }
  };

  const handleOtp = async () => {
    try {
      setLoading(true);
      await fetchWrapper('/api/proxy/users/verify-credentials-otp', {
        body: JSON.stringify({ otp }),
        method: 'POST',
        // suppressToast: true,
      });
      setLoading(false);
      setShowOtpModal(false);
      toast.success(
        otpFor === 'email'
          ? 'Email updated successfully'
          : 'Mobile updated successfully'
      );
    } catch (error: any) {
      console.log(error);
      if (error?.message && typeof error?.message === 'string') {
        setOtpErr(error.message as string);
      } else {
        setOtpErr('Something went wrong, Plesae try again');
      }
    } finally {
      setLoading(false);
    }
  };

  const handleResendOtp = async () => {
    let data: updateCredentials = {
      email: userDetails.email.trim(),
      type: 'email',
    };
    if (otpFor === 'mobile' && userDetails.mobile) {
      data = { mobile: Number(userDetails.mobile.trim()), type: 'mobile' };
    }
    setLoading(true);
    const res = await updateCredentials({
      data,
    });
    console.log(res);
    setLoading(false);
  };

  const handlePersonalInterest = async ({
    id,
    name,
  }: {
    id: string;
    name: string;
  }) => {
    dispatch(toggleUserPersonalInterest({ id, name }));
    const formData = new FormData();
    formData.append(
      'profileData',
      JSON.stringify({
        personalInterest: id,
      })
    );
    const res = await updateProfile({ formData });
    //if res is not true then the toggle again the personalInterest to go back to prev state
    if (res !== true) {
      dispatch(toggleUserPersonalInterest({ id, name }));
    }
  };

  const handleSendNotification = async () => {
    dispatch(
      setUserDetails({ sendNotification: !userDetails.sendNotification })
    );
    const formData = new FormData();
    formData.append(
      'profileData',
      JSON.stringify({
        sendNotification: !userDetails.sendNotification,
      })
    );
    const res = await updateProfile({ formData });
    //if res is not true then the toggle again the button to go back to prev state
    if (res !== true) {
      dispatch(
        setUserDetails({ sendNotification: !userDetails.sendNotification })
      );
    }
  };

  return (
    <>
      <CommonHeader headline='Profile Settings' />
      <motion.section
        animate={{ opacity: 1 }}
        className='bg-[#E0E0E0] dark:bg-[#292B31] h-full lg:min-h-[calc(100svh-112px)] mb-0 header-container pb-[40px]'
        initial={{ opacity: 0 }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: 'Profile Settings' },
            ]}
          />
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='pt-[35px] lg:pt-[15px]'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.2 }}
        >
          <motion.div
            animate={{ scale: 1, opacity: 1 }}
            className='relative w-[90px] h-[90px] lg:w-[108px] lg:h-[108px] rounded-full mx-auto'
            initial={{ scale: 0.8, opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
            whileHover={{
              boxShadow: '0px 0px 15px rgba(0, 0, 0, 0.2)',
              transition: { duration: 0.3 },
            }}
          >
            <Image
              alt='user pic'
              className='object-cover rounded-full'
              fill
              src={userDetails?.avatar || ''}
            />
            <motion.div
              animate={{ opacity: 1 }}
              className='absolute bottom-[10px] right-0 w-[19px] h-[19px] lg:w-[23px] lg:h-[23px] rounded-full flex-center bg-[#D6D6D6] cursor-pointer'
              initial={{ opacity: 0 }}
              onClick={() => {
                if (fileInputRef.current) {
                  fileInputRef.current.click();
                }
              }}
              transition={{ delay: 0.5, duration: 0.3 }}
              whileHover={{ scale: 1.2, backgroundColor: '#c0c0c0' }}
              whileTap={{ scale: 0.9 }}
            >
              <input
                accept='.png, .jpg, .jpeg'
                className='w-[100px] hidden'
                onChange={handleImageChange}
                ref={fileInputRef}
                type='file'
              />
              <Image
                alt='icon'
                className='w-[11px] h-[11px]'
                height={15}
                src={'/svg/pencil.svg'}
                width={15}
              />
            </motion.div>
          </motion.div>
          {uploadingImg && <LoadingGif className='!w-[30px]' />}
          {imgErr && (
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='text-[red] text-[10px] text-center pt-[10px]'
              initial={{ opacity: 0, y: 10 }}
              transition={{ duration: 0.3 }}
            >
              {imgErr}
            </motion.div>
          )}
        </motion.div>

        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='bg-container relative dark:bg-[#2d2e32] rounded-[10px] px-[8px] py-[30px] lg:px-[20px] mx-[6px] lg:mx-[30px] flex-center gap-y-[12px] gap-x-[15px] mt-[12px] xl:w-[85%] xl:mx-auto lg:mt-[25px]'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 0.4 }}
          whileHover={{
            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
            transition: { duration: 0.3 },
          }}
        >
          <motion.div
            animate={{ opacity: 1 }}
            className='grow-0 w-[457px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <motion.h4
              animate={{ opacity: 1, x: 0 }}
              className='text-xs lg:text-sm font-pat ml-[12px] lg:absolute lg:left-[20px] top-[30px]'
              initial={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              Basic Info
            </motion.h4>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.7 }}
            >
              <InputFieldWithIcon2
                iconUrl='/svg/forms/user.svg'
                label='Full Name'
                onChange={(name) => dispatch(setUserDetails({ name: name }))}
                onSubmit={handleName}
                placeholder='Enter Full Name'
                rootClass='mt-[15px]'
                value={userDetails.name}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.8 }}
            >
              <InputFieldWithIcon2
                iconUrl='/svg/forms/email.svg'
                label='Enter Mail Address'
                onChange={(email) => dispatch(setUserDetails({ email }))}
                onSubmit={handleEmail}
                placeholder='Enter Mail Address'
                rootClass='mt-[15px]'
                value={userDetails.email}
              />
            </motion.div>
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              initial={{ opacity: 0, y: 20 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <InputFieldWithIcon2
                iconUrl='/svg/forms/phone.svg'
                label='Mobile Number'
                onChange={(mobile) => dispatch(setUserDetails({ mobile }))}
                onSubmit={handleMobile}
                placeholder='Enter Phone Number'
                rootClass='mt-[15px]'
                value={userDetails.mobile}
              />
            </motion.div>
          </motion.div>
        </motion.div>

        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='bg-container dark:bg-[#2d2e32] rounded-[10px] px-[10px] mx-[6px] lg:mx-[30px] mt-[17px] xl:w-[85%] xl:mx-auto py-[30px]'
          initial={{ opacity: 0, y: 20 }}
          transition={{ duration: 0.5, delay: 1 }}
          whileHover={{
            boxShadow: '0px 8px 30px rgba(0, 0, 0, 0.1)',
            transition: { duration: 0.3 },
          }}
        >
          <motion.h4
            animate={{ opacity: 1, x: 0 }}
            className='text-xs lg:text-sm font-pat ml-[12px] lg:ml-[20px]'
            initial={{ opacity: 0, x: -20 }}
            transition={{ duration: 0.5, delay: 1.1 }}
          >
            Personal Interests
          </motion.h4>
          <motion.div
            animate={{ opacity: 1 }}
            className='lg:px-[20px] flex flex-wrap gap-x-[8px] gap-y-[10px] mt-[20px]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 1.2 }}
          >
            {personalInterest.map((item, index) => (
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                initial={{ opacity: 0, y: 20 }}
                key={item.id}
                transition={{ duration: 0.3, delay: 1.2 + index * 0.05 }}
              >
                <PillButton
                  className='border-[1px] border-primary'
                  isSelected={
                    userDetails.personalInterest.findIndex(
                      (element) => element.id === item.id
                    ) !== -1
                  }
                  key={item.id}
                  onClick={() =>
                    handlePersonalInterest({ id: item.id, name: item.name })
                  }
                  text={item.name}
                />
              </motion.div>
            ))}
          </motion.div>
          <motion.div
            animate={{ opacity: 1, scaleX: 1 }}
            className='mx-[5px] lg:mx-[20px] my-[30px] h-[1px] bg-[#D7D7D7]'
            initial={{ opacity: 0, scaleX: 0 }}
            transition={{ duration: 0.5, delay: 1.3 }}
          />
          <motion.div
            animate={{ opacity: 1 }}
            className='flex items-center justify-start user-subscription'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 1.4 }}
          >
            <motion.span
              animate={{ opacity: 1, x: 0 }}
              className='text-[10px] lg:text-xs font-semibold inline-block ml-[10px] lg:ml-[30px] mr-[20px]'
              initial={{ opacity: 0, x: -20 }}
              transition={{ duration: 0.5, delay: 1.5 }}
            >
              Send interest based notifications
            </motion.span>
            <motion.div
              animate={{ opacity: 1, scale: 1 }}
              initial={{ opacity: 0, scale: 0.8 }}
              transition={{ duration: 0.5, delay: 1.6 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
            >
              <Switch
                checked={userDetails.sendNotification}
                checkedChildren={
                  <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
                    On
                  </span>
                }
                className='!bg-white dark:!bg-[#555966] shadow-md'
                defaultChecked
                onChange={handleSendNotification}
                unCheckedChildren={
                  <span className='text-[9px] lg:text-[10px] translate-y-[-1px] font-medium text-blackWhite inline-block'>
                    Off
                  </span>
                }
              />
            </motion.div>
          </motion.div>
        </motion.div>
      </motion.section>

      <CommonOtpModal
        isOpen={isShowOtpModal}
        onClose={() => setShowOtpModal(!isShowOtpModal)}
        title={otpFor === 'email' ? 'Verify Email' : 'Verify Mobile'}
      >
        <>
          <div className='text-blackWhite text-[10px] lg:text-[12px] text-center font-medium mt-[20px] rounded-[5px] lg:mt-[30px] px-[20px]'>
            We have sent an OTP to your {otpFor}{' '}
            <span className='font-bold'>
              {otpFor === 'email' ? userDetails?.email : userDetails?.mobile}
            </span>
            , Please confirm
          </div>

          <div className='px-[30px] lg:px-[20px] mt-[20px] lg:mt-[25px] mx-auto max-w-[400px]'>
            <label className='text-[10px] font-medium text-[#767676] dark:text-[#929090]'>
              Enter OTP <span className='text-[#F00] sup'>*</span>
            </label>
            <OTPInput
              containerStyle='w-full h-[40px] flex items-center justify-between lg:mt-[5px]'
              inputStyle='!w-[37px] h-[35px]  lg:!w-[51px] lg:h-[48px] outline-none rounded-[5px] border border-primary text-blackWhite lg:text-[16px] font-semibold'
              inputType='number'
              numInputs={4}
              onChange={(value) => setOtp(Number(value))}
              renderInput={(props) => <input {...props} />}
              shouldAutoFocus={true}
              value={otp?.toString()}
            />
            {otpErr && (
              <span className='text-[#F00] text-[10px] font-light'>
                {otpErr}
              </span>
            )}
            {isLoading ? (
              <LoadingGif className='mt-[30px]' />
            ) : (
              <div className='flex-center gap-x-[30px] mt-[40px]'>
                <ThemeButton
                  className='!w-[90px] lg:w-[100px] hover:bg-primaryDark active:opacity-80'
                  isDisabled={isLoading}
                  onClick={handleOtp}
                  text='VERIFY'
                />
                <button
                  className={clsx(
                    isLoading && 'opacity-40',
                    'text-[12px] font-medium text-blackWhite'
                  )}
                  disabled={isLoading}
                  onClick={handleResendOtp}
                >
                  Resend OTP
                </button>
              </div>
            )}
          </div>
        </>
      </CommonOtpModal>
    </>
  );
};

export default IndexClientsProfileSetting;
