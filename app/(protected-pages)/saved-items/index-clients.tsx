'use client';
import React, { useEffect, useState } from 'react';
import CommonHeader from '../../components/headers/common-header';
import TabsContainer from '../../components/atoms/tabs-container';
import CommonToolbar from '../../components/common-toolbar';
import MainOfferCard from '../../components/cards/main-offer-card';
import PlusIcon from '../../components/svg/plus-icon';
import CopySVG from '../../components/svg/copy';
import StoreByCBCard from '../../components/landing/stores-cb-percentage/store-by-cb-card';
import Giftcard from '../../components/cards/giftcard';
import AsideSavedItems from './aside-saved-items';
import type {
  GetAllStoresResponse,
  GetGiftCardListResponse,
  SavedCouponsResponse,
  SavedDealsResponse,
} from '@/services/api/data-contracts';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { ConfigProvider, Pagination, theme } from 'antd';
import { useTheme } from 'next-themes';
import {
  useCopyCoupon,
  useCreateMultiQueryString,
  useResponsiveGrid,
} from '@/utils/custom-hooks';
import CommonFilterMobile from '../../components/misc/common-filter-mobile';
import { formatIndRs } from '@/utils/helpers';
import NoData from '@/app/components/no-data';
import { motion, AnimatePresence } from 'framer-motion';

const IndexClientsSavedItems = ({
  savedDeals,
  savedCoupons,
  savedStores,
  savedGiftCards,
}: {
  savedDeals: SavedDealsResponse;
  savedCoupons: SavedCouponsResponse;
  savedStores: GetAllStoresResponse;
  savedGiftCards: GetGiftCardListResponse;
}) => {
  const [activeTabId, setActiveTabId] = useState(1);
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const [pagination, setPagination] = useState(savedDeals?.pagination);
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const handleCopyCoupon = useCopyCoupon();

  // Use our custom responsive grid hook
  const { getGridProps } = useResponsiveGrid();
  useEffect(() => {
    // Update paginationData based on activeTabId
    if (activeTabId === 1) {
      setPagination(savedDeals?.pagination);
    } else if (activeTabId === 2) {
      setPagination(savedCoupons?.pagination);
    } else if (activeTabId === 3) {
      setPagination(savedStores?.pagination);
    } else if (activeTabId === 4) {
      setPagination(savedGiftCards?.pagination);
    }
  }, [
    activeTabId,
    savedDeals?.pagination,
    savedCoupons?.pagination,
    savedStores?.pagination,
    savedGiftCards?.pagination,
  ]);

  return (
    <>
      <CommonHeader
        headline='Saved Items'
        subHeading={
          <>
            <span>Saved Items</span>
            <span className='ml-[6px] text-[7px] sm:text-[8px] font-nexa'>
              {`${pagination?.pageSize}/${pagination?.total}`}
            </span>
          </>
        }
      />

      <motion.section
        animate={{ opacity: 1 }}
        className='header-container'
        initial={{ opacity: 0 }}
        onClick={(e) => {
          e.stopPropagation();
        }}
        transition={{ duration: 0.5 }}
      >
        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex lg:mx-[60px] min-[1440px]:mx-auto sticky top-[64px] lg:top-[103px] z-[9]'
          initial={{ opacity: 0, y: -20 }}
          transition={{ duration: 0.5, delay: 0.1 }}
        >
          <motion.div
            animate={{ opacity: 1 }}
            className='pl-[30px] lg:flex items-center hidden w-[230px] xl:w-[280px] bg-primary text-white text-sm font-pat'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.2 }}
          >
            Saved Items
          </motion.div>
          <motion.div
            animate={{ opacity: 1 }}
            className='overflow-auto grow scrollbarNone lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32]'
            initial={{ opacity: 0 }}
            transition={{ duration: 0.5, delay: 0.3 }}
          >
            <TabsContainer
              activeId={activeTabId}
              items={[
                { id: 1, label: 'Deals' },
                { id: 2, label: 'Coupons' },
                { id: 3, label: 'Stores' },
                // { id: 4, label: 'Giftcards' },
                // { id: 5, label: 'Blogs' },
                // { id: 6, label: 'Important Pages' },
              ]}
              setActiveId={setActiveTabId}
            />
          </motion.div>
        </motion.div>
        <motion.div
          animate={{ opacity: 1 }}
          className='flex lg:mx-[60px] min-[1440px]:mx-auto'
          initial={{ opacity: 0 }}
          transition={{ duration: 0.5, delay: 0.4 }}
        >
          <AsideSavedItems tabId={activeTabId} />
          <motion.div
            animate={{ opacity: 1, x: 0 }}
            className='w-full'
            initial={{ opacity: 0, x: 30 }}
            transition={{ duration: 0.5, delay: 0.5 }}
          >
            <motion.div
              animate={{ opacity: 1, y: 0 }}
              className='bg-[#E1E2E4] dark:bg-container sticky top-[107px] lg:top-[146px] z-[9]'
              initial={{ opacity: 0, y: -10 }}
              transition={{ duration: 0.5, delay: 0.6 }}
            >
              <CommonToolbar
                onClickFilterBtn={() => setShowFilterModal(!isShowFilterModal)}
                rootClassName='bg-transparent lg:bg-[#E1E2E4] lg:dark:bg-[#2D313A]'
              />

              <motion.h4
                animate={{ opacity: 1 }}
                className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite p-4  mx-[6px] px-[8px]'
                initial={{ opacity: 0 }}
                transition={{ duration: 0.5, delay: 0.7 }}
              >
                Results{' '}
                <motion.span
                  animate={{ opacity: 1 }}
                  className='text-xs font-nexa font-[800]'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5, delay: 0.8 }}
                >
                  ({pagination?.pageSize + '/' + pagination?.total})
                </motion.span>
              </motion.h4>
            </motion.div>

            <motion.div
              animate={{ opacity: 1 }}
              className='bg-container mx-[6px] px-[8px] py-[20px]'
              initial={{ opacity: 0 }}
              transition={{ duration: 0.5, delay: 0.9 }}
            >
              <AnimatePresence mode='wait'>
                {activeTabId === 1 &&
                  (savedDeals?.deals?.length ? (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      {...getGridProps()}
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      transition={{ duration: 0.5 }}
                    >
                      {savedDeals?.deals?.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={item.uid}
                          transition={{ delay: index * 0.05, duration: 0.5 }}
                          whileHover={{
                            scale: 1.03,
                            transition: { duration: 0.2 },
                          }}
                        >
                          <MainOfferCard
                            duration={item.endDate}
                            hideCbTag={item?.hideCbTag}
                            isOfferUpto={Boolean(item?.offerCaption?.trim())}
                            key={item.uid}
                            offerTitle={item.offerTitle}
                            productImgUrl={item.productImage || ''}
                            rootClass='!w-full'
                            saved={item.saved}
                            showNewBadge={false}
                            storeImgUrl={item.storeLogoUrl}
                            storeName={item.storeName}
                            uid={item.uid}
                          >
                            {/* first child */}
                            <p
                              dangerouslySetInnerHTML={{
                                __html: item?.offerTitle ?? '',
                              }}
                            />
                            {/* second child */}
                            {item.salePrice && item.salePrice > 0 && (
                              <p className='text-primary font-medium text-[9px]'>
                                Offer Applied Price
                                <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                                  {formatIndRs(item?.salePrice)}
                                </span>
                              </p>
                            )}

                            {/* this child added for order wrong bug in ongoing sale offers */}
                            <></>

                            {/* third child */}
                            {!item?.hideCbTag ? (
                              <>
                                <PlusIcon className='text-black' />
                                <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                                  {item.offerCaption}
                                </p>
                              </>
                            ) : (
                              <></>
                            )}

                            {/* fourth child */}
                            <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                              <span className='text-[10px] font-semibold text-white'>
                                GRAB DEAL
                              </span>
                            </div>
                          </MainOfferCard>
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      initial={{ opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <NoData />
                    </motion.div>
                  ))}
                {activeTabId === 2 &&
                  (savedCoupons && savedCoupons.coupons?.length ? (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      className='grid grid-cols-2 gap-x-[8px] gap-y-[16px] md:grid-cols-3 xl:grid-cols-4 lg:gap-[24px] justify-items-center'
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      transition={{ duration: 0.5 }}
                    >
                      {savedCoupons?.coupons?.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={item.uid}
                          transition={{ delay: index * 0.05, duration: 0.5 }}
                          whileHover={{
                            scale: 1.03,
                            transition: { duration: 0.2 },
                          }}
                        >
                          <MainOfferCard
                            duration={item.endDate}
                            isFromSavedScreen={true}
                            isOfferUpto={Boolean(item?.offerCaption?.trim())}
                            key={item.uid}
                            offerTitle={item.offerTitle}
                            productImgUrl={item.productImage || ''}
                            rootClass='!w-full'
                            saved={item.saved}
                            showNewBadge={false}
                            storeImgUrl={item.storeLogoUrl}
                            storeName={item.storeLogoUrl}
                            uid={item.uid}
                          >
                            {/* first child */}
                            <p
                              dangerouslySetInnerHTML={{
                                __html: item.offerTitle,
                              }}
                            />
                            {/* second child */}

                            {item.salePrice > 0 && (
                              <p className='text-primary dark:text-white font-medium text-[9px]'>
                                Offer Applied Price
                                <span className='font-nexa font-black ml-[4px] text-[10px]'>
                                  {formatIndRs(item.salePrice)}
                                </span>
                              </p>
                            )}

                            {/* third child */}
                            <>
                              <PlusIcon className='text-black' />
                              <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                                {item.offerCaption}
                              </p>
                            </>
                            {/* fourth child */}
                            {item?.couponCode && (
                              <button
                                className='bg-primary w-full p-[4px] h-full rounded-b-[6px] relative hover:bg-[#5448b0] active:bg-primary'
                                onClick={(event) =>
                                  handleCopyCoupon({
                                    event,
                                    uid: item.uid,
                                    couponCode: item.couponCode,
                                  })
                                }
                                type='button'
                              >
                                <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                                  <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                                    Copy Code
                                  </span>
                                  <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                                </div>
                              </button>
                            )}
                          </MainOfferCard>
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      initial={{ opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <NoData />
                    </motion.div>
                  ))}

                {activeTabId === 3 &&
                  (savedStores?.stores?.length ? (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      className='grid grid-cols-2 gap-x-[8px] gap-y-[16px] md:grid-cols-3 xl:grid-cols-4 lg:gap-[24px]'
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      transition={{ duration: 0.5 }}
                    >
                      {savedStores?.stores?.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={item.uid}
                          transition={{ delay: index * 0.05, duration: 0.5 }}
                          whileHover={{
                            scale: 1.03,
                            transition: { duration: 0.2 },
                          }}
                        >
                          <StoreByCBCard
                            bgColor={item.bgColor}
                            caption={item.caption}
                            fromSavedScreen={true}
                            key={item.uid}
                            saved={item.saved}
                            src={item.imageUrl}
                            storeName={item.storeName}
                            uid={item.uid}
                          />
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      initial={{ opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <NoData />
                    </motion.div>
                  ))}

                {activeTabId === 4 &&
                  (savedGiftCards && savedGiftCards?.giftCards?.length ? (
                    <motion.div
                      animate={{ opacity: 1, y: 0 }}
                      className='grid grid-cols-2 md:grid-cols-3 xl:grid-cols-4 gap-[8px] justify-items-center'
                      exit={{ opacity: 0, y: -20 }}
                      initial={{ opacity: 0, y: 20 }}
                      transition={{ duration: 0.5 }}
                    >
                      {savedGiftCards?.giftCards?.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={item.uid}
                          transition={{ delay: index * 0.05, duration: 0.5 }}
                          whileHover={{
                            scale: 1.03,
                            transition: { duration: 0.2 },
                          }}
                        >
                          <Giftcard
                            caption={item.caption}
                            fromSavedScreen={true}
                            imgUrl={item.imageUrl}
                            key={item.uid}
                            rootClass='!w-full'
                            saved={item.saved}
                            title={item.name}
                            uid={item.uid}
                          />
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <motion.div
                      animate={{ opacity: 1 }}
                      exit={{ opacity: 0 }}
                      initial={{ opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      <NoData />
                    </motion.div>
                  ))}
              </AnimatePresence>

              {/* {activeTabId === 5 && (
								<div className="grid grid-cols-2 gap-x-[8px] gap-y-[16px] md:grid-cols-3 xl:grid-cols-4 lg:gap-[24px]">
									{savedStores?.stores?.map((_, index) => (
										<BlogCard
											blogImg={`/temp/blog1.png`}
											blogTitle="It's Important to invest in Digital Gold!"
											category="Investment"
											key={index}
											postedOn="21 December 2021"
											readTime="2min Read"
											rootClass="!w-full"
											userName="Ashish"
											userPic="/temp/profile.png"
										/>
									))}
								</div>
							)}
							{activeTabId === 6 && (
								<div className="grid grid-cols-2 gap-x-[8px] gap-y-[16px] md:grid-cols-3 xl:grid-cols-4 lg:gap-[24px]">
									{[...Array(10)].map((_, index) => (
										<SavedPageCard
											filterApplied={1}
											imgUrl={`/temp/giftcards/giftcard1.png`}
											key={index}
											searchValue="Flipkart"
											sortApplied={1}
											title="Giftcard"
										/>
									))}
								</div>
							)} */}
            </motion.div>

            {pagination?.pageSize > 0 && (
              <motion.div
                animate={{ opacity: 1, y: 0 }}
                className='flex-center w-full my-[50px]'
                initial={{ opacity: 0, y: 20 }}
                transition={{ duration: 0.5, delay: 1 }}
              >
                <ConfigProvider
                  theme={{
                    algorithm:
                      resolvedTheme === 'dark'
                        ? theme.darkAlgorithm
                        : theme.defaultAlgorithm,
                  }}
                >
                  <Pagination
                    defaultCurrent={1}
                    defaultPageSize={15}
                    onChange={(pageNumber, pageSize) =>
                      replace(
                        pathname +
                          '?' +
                          createMultiQueryString([
                            { name: 'page', value: pageNumber.toString() },
                            { name: 'pageSize', value: pageSize.toString() },
                          ])
                      )
                    }
                    total={pagination?.total}
                  />
                </ConfigProvider>
              </motion.div>
            )}
          </motion.div>
        </motion.div>
      </motion.section>
      <CommonFilterMobile
        filterProps={
          activeTabId === 1
            ? [
                {
                  filter: 'user',
                },
              ]
            : activeTabId === 2
            ? [
                {
                  filter: 'user',
                },
              ]
            : activeTabId === 3
            ? [{ filter: 'percentage' }]
            : []
        }
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexClientsSavedItems;
