'use client';
import React, { useState } from 'react';
import CommonHeader from '../../components/headers/common-header';
import TabsContainer from '../../components/atoms/tabs-container';
import TransactionCard from '../../components/cards/transaction-card';
import CommonToolbar from '../../components/common-toolbar';

const transactionsData = [
  {
    imgUrl: '/temp/giftcard.png',
    title: 'Indiancashback Giftcard',
    amount: '₹5000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Pending',
  },
  {
    imgUrl: '/temp/giftcard.png',
    title: 'Indiancashback Giftcard',
    amount: '₹5000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/giftcard.png',
    title: 'Indiancashback Giftcard',
    amount: '₹5000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Success',
  },
  {
    imgUrl: '/temp/withdraw.png',
    title: 'Cashback Withdrawal ',
    amount: '₹2000.00',
    transactionType: 'Pending',
  },
];
const IndexClientsTransactions = () => {
  const [activeTabId, setActiveTabId] = useState(1);
  return (
    <>
      <CommonHeader
        headline='Transactions'
        subHeading={
          <>
            <span>All Transaction</span>
            <span className='ml-[6px] text-[7px] sm:text-[8px] font-nexa'>
              25/1254
            </span>
          </>
        }
      />

      <section
        className='header-container'
        onClick={(e) => {
          e.stopPropagation();
        }}
      >
        <div className='sticky top-[64px] lg:top-[103px] bg-container lg:mx-[60px] min-[1440px]:mx-auto'>
          <div className='flex '>
            <div className='pl-[30px] lg:flex items-center hidden w-[230px] xl:w-[280px] bg-primary text-white text-sm font-pat'>
              Transactions
            </div>
            <div className='overflow-auto grow scrollbarNone lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32]'>
              <TabsContainer
                activeId={activeTabId}
                items={[
                  { id: 1, label: 'All' },
                  { id: 2, label: 'Bill Payments' },
                  { id: 3, label: 'Investments' },
                  { id: 4, label: 'Cashback' },
                  { id: 5, label: 'Giftcards' },
                ]}
                setActiveId={setActiveTabId}
              />
            </div>
          </div>
          <CommonToolbar
            hideFilter={true}
            rootClassName='bg-transparent lg:bg-[#E1E2E4] lg:dark:bg-[#2D313A]'
            showDatePicker={true}
          />
        </div>
        <div className='flex flex-col gap-y-[9px] py-[12px] lg:py-[30px] mx-[6px] lg:mx-[60px] min-[1440px]:mx-auto px-[8px] lg:px-[10px] min-[1440px]:px-[20px] bg-container'>
          {transactionsData.map((item, index) => (
            <TransactionCard
              amount={item.amount}
              date='22 January 2022'
              imgUrl={item.imgUrl}
              key={index}
              time='12.36 PM'
              title={item.title}
              transactionType={item.transactionType as 'Pending' | 'Success'}
            />
          ))}
        </div>
      </section>
    </>
  );
};

export default IndexClientsTransactions;
