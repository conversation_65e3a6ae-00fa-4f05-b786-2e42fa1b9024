'use client';
import React, { useEffect, useState } from 'react';
import FilterSVG from '../svg/filter';
import { Checkbox, Radio, Space } from 'antd';
import ClearAllBtn from '@/app/components/atoms/clear-all-btn';
import clsx from 'clsx';
import { motion, AnimatePresence } from 'framer-motion';
import type { SidebarFilterProps } from '@/types/global-types';
// import { PercentageSliderFormatter } from '../atoms/percentage-slider-formatter';
import { SelectedPill } from '../atoms/pills';
import { usePathname, useSearchParams } from 'next/navigation';
import { useRouter } from 'next/navigation';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import {
  useCreateQueryString,
  // useCreateMultiQueryString,
} from '@/utils/custom-hooks';
import {
  setCategories,
  setOnGoingSalesList,
  setFilteredCategories,
  setSearchValueCat,
  setSelectedOfferType,
  setSelectedSubCategories,
  setSelectedUserType,
  setSelectedOnGoingSales,
  setPercentageFilter,
} from '@/redux/slices/common-filters-slice';
import {
  AllCategoriesResponse,
  GetAllOnGoingOffersResponse,
  OfferTypes,
  UserTypes,
} from '@/services/api/data-contracts';
import {
  filterSubcategoriesByUid,
  filterOnGoingSalesByUid,
  searchSubCategoriesByName,
  toggleCategory,
} from '@/utils/helpers';
import SearchInput from '../atoms/search-input';
import { ArrowDown } from '../svg/arrow-up-down';
import fetchWrapper from '@/utils/fetch-wrapper';

const CommonFilterSidebar = (SidebarFilterProps: SidebarFilterProps) => {
  const { rootClass, filterProps } = SidebarFilterProps;
  const pathname = usePathname();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const {
    selectedUserType,
    selectedOfferType,
    categories,
    filteredCategories,
    onGoingSalesList,
    selectedSubCategories,
    selectedOnGoingSales,
    searchValueCat,
    // percentageFilter,
  } = useAppSelector((state) => state.commonFilters);
  const dispatch = useAppDispatch();
  const createQueryString = useCreateQueryString(searchParams);
  // const createMultiQueryString = useCreateMultiQueryString(searchParams);

  const [showMore, setShowMore] = useState(false);

  const salesChangeHandler = ({ uid, name }: { uid: number; name: string }) => {
    const newSelectedOnGoingSales = toggleCategory({
      arr: selectedOnGoingSales,
      uid,
      name,
    });
    dispatch(setSelectedOnGoingSales(newSelectedOnGoingSales));

    const newSelectedOnGoingSalesString = newSelectedOnGoingSales
      .reduce<number[]>((total, item) => total.concat(item.uid), [])
      .join(',');
    replace(
      pathname + '?' + createQueryString('sales', newSelectedOnGoingSalesString)
    );
  };

  useEffect(() => {
    async function getAllCategoriesData() {
      const res = await fetchWrapper<AllCategoriesResponse[]>(
        '/api/proxy/context/category/all-categories-details',
        {
          method: 'GET',
          excludeCredentials: true,
        }
      );
      return res;
    }

    getAllCategoriesData().then((data: Array<AllCategoriesResponse>) => {
      dispatch(setCategories(data));
      dispatch(setFilteredCategories(data));
    });
  }, [dispatch]);

  useEffect(() => {
    async function getAllSalesData() {
      const res = await fetchWrapper<GetAllOnGoingOffersResponse[]>(
        '/api/proxy/context/offers/all-on-going-offers',
        {
          method: 'GET',
          excludeCredentials: true,
        }
      );
      return res;
    }

    getAllSalesData().then((data: Array<GetAllOnGoingOffersResponse>) => {
      dispatch(setOnGoingSalesList(data));
    });
  }, [dispatch]);

  useEffect(() => {
    dispatch(
      setPercentageFilter({
        minPercent: Number(searchParams.get('minPercent')) || 0,
        maxPercent: Number(searchParams.get('maxPercent')) || 100,
      })
    );
    dispatch(
      setSelectedUserType((searchParams.get('userType') as UserTypes) || 'both')
    );
    dispatch(
      setSelectedOfferType(
        (searchParams.get('offerType') as OfferTypes) || 'deals'
      )
    );

    const subCategories =
      searchParams.get('subCategories')?.split(',').map(Number) || [];

    const selectedSubCategoriesIds = filterSubcategoriesByUid(
      categories,
      subCategories
    );
    dispatch(setSelectedSubCategories(selectedSubCategoriesIds));

    const salesList = searchParams.get('sales')?.split(',').map(Number) || [];
    const selectedOnGoingSalesListIds = filterOnGoingSalesByUid(
      onGoingSalesList,
      salesList
    );
    dispatch(setSelectedOnGoingSales(selectedOnGoingSalesListIds));
  }, [searchParams, onGoingSalesList, categories, dispatch]);

  // const onChangePercentage = (value: {
  //   minPercent: number;
  //   maxPercent: number;
  // }) => {
  //   dispatch(setPercentageFilter(value));
  //   const queries = [
  //     { name: 'minPercent', value: value.minPercent.toString() },
  //     { name: 'maxPercent', value: value.maxPercent.toString() },
  //   ];

  //   const queryString = createMultiQueryString(queries);
  //   replace(pathname + '?' + queryString);
  // };

  const onChangeUser = ({ userType }: { userType: UserTypes }) => {
    dispatch(setSelectedUserType(userType));
    replace(pathname + '?' + createQueryString('userType', userType));
  };
  const onChangeOffer = ({ offerType }: { offerType: OfferTypes }) => {
    dispatch(setSelectedOfferType(offerType));
    replace(pathname + '?' + createQueryString('offerType', offerType));
  };

  useEffect(() => {
    if (searchValueCat) {
      const filteredCats = searchSubCategoriesByName({
        categories: Array.from(categories),
        query: searchValueCat,
      });
      dispatch(setFilteredCategories(filteredCats));
    } else {
      dispatch(setFilteredCategories(categories));
    }
  }, [searchValueCat, categories, dispatch]);

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      className={clsx(
        rootClass,
        'bg-container pb-[40px] mt-[8px] h-full overflow-auto customScrollbar'
      )}
      initial={{ opacity: 0, y: 10 }}
      transition={{ duration: 0.5, ease: 'easeOut' }}
    >
      <motion.div
        animate={{ opacity: 1 }}
        className='flex pl-[20px] items-center sticky top-0 h-[45px] bg-container z-[1]'
        initial={{ opacity: 0 }}
        transition={{ delay: 0.2, duration: 0.4 }}
      >
        <FilterSVG className='text-primary w-[15px] h-[17px]' />
        <span className='text-blackWhite ml-[16px] text-sm font-pat font-normal'>
          Filters
        </span>
      </motion.div>

      <motion.div
        animate={{ opacity: 1 }}
        className='px-[10px]'
        initial={{ opacity: 0 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        {/* -----------------------hidden for phase 1--------------- */}
        {/* {filterProps.some((item) => item.filter === 'percentage') && (
          <>
            <h4 className='mt-[20px] text-blackWhite text-xs font-semibold'>
              Percentage
            </h4>
            <Slider
              className='mt-[20px] mx-[20px]'
              classNames={{
                track: '!bg-[#7366D9]',
                rail: '!bg-[#E7E9EB] dark:!bg-black',
              }}
              max={100}
              min={0}
              onChange={(value) =>
                onChangePercentage({
                  minPercent: value[0],
                  maxPercent: value[1],
                })
              }
              range
              tooltip={{ formatter: PercentageSliderFormatter }}
              value={[percentageFilter.minPercent, percentageFilter.maxPercent]}
            />
          </>
        )} */}
        {filterProps.some((item) => item.filter === 'sale') && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='pl-[10px]'
            initial={{ opacity: 0, y: 5 }}
            transition={{ delay: 0.4, duration: 0.3 }}
          >
            <div className='flex justify-between mt-[20px]'>
              <span className='text-blackWhite text-xs font-semibold '>
                Select Sales
              </span>
              {selectedOnGoingSales?.length > 0 && (
                <ClearAllBtn
                  onClick={() => {
                    dispatch(setSelectedOnGoingSales([]));
                    replace(pathname + '?' + createQueryString('sales', ''));
                  }}
                />
              )}
            </div>

            <div className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'>
              {selectedOnGoingSales.map((item) => (
                <SelectedPill
                  key={item.uid}
                  onClick={() => {
                    const newSelectedUids = selectedOnGoingSales.filter(
                      (itm) => itm.uid !== item.uid
                    );
                    dispatch(setSelectedSubCategories(newSelectedUids));
                    const newSelectedOnGoingSalesString = newSelectedUids
                      .reduce<number[]>(
                        (total, item) => total.concat(item.uid),
                        []
                      )
                      .join(',');

                    replace(
                      pathname +
                        '?' +
                        createQueryString(
                          'sales',
                          newSelectedOnGoingSalesString
                        )
                    );
                  }}
                  text={item.name}
                />
              ))}
            </div>

            <div className='mt-[16px] flex flex-col gap-y-[12px]'>
              {(onGoingSalesList.length > 5 && !showMore
                ? onGoingSalesList.slice(0, 5)
                : onGoingSalesList
              ).map((item) => (
                <Checkbox
                  checked={selectedOnGoingSales.some(
                    (ele) => ele.uid === item.uid
                  )}
                  key={item.uid}
                  onChange={() =>
                    salesChangeHandler({ uid: item.uid, name: item.name })
                  }
                  value={item.uid}
                >
                  <span className='text-blackWhite text-[12px] font-normal'>
                    {item.name}
                  </span>
                </Checkbox>
              ))}

              {/*  More Button  */}
              {onGoingSalesList.length > 5 && (
                <div className='flex justify-center my-[5px]'>
                  <button
                    className='text-primary text-[12px] font-normal'
                    onClick={() => setShowMore(!showMore)}
                  >
                    {showMore ? 'Show Less' : 'Show More'}
                  </button>
                </div>
              )}
            </div>
          </motion.div>
        )}
        {filterProps.some((item) => item.filter === 'user') && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='pl-[10px]'
            initial={{ opacity: 0, y: 5 }}
            transition={{ delay: 0.5, duration: 0.3 }}
          >
            <h4 className='mt-[20px] mb-[10px] text-blackWhite text-xs font-semibold'>
              User Type
            </h4>
            <Radio.Group
              defaultValue={'both'}
              onChange={(e) => onChangeUser({ userType: e.target.value })}
              value={selectedUserType}
            >
              <Space direction='vertical'>
                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'both'}
                >
                  All Users
                </Radio>
                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'new'}
                >
                  New User
                </Radio>
                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'existing'}
                >
                  Old User
                </Radio>
              </Space>
            </Radio.Group>
          </motion.div>
        )}
        {filterProps.some((item) => item.filter === 'offer') && (
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='pl-[10px]'
            initial={{ opacity: 0, y: 5 }}
            transition={{ delay: 0.6, duration: 0.3 }}
          >
            <h4 className='mt-[20px] mb-[10px] text-blackWhite text-xs font-semibold'>
              Offer Type
            </h4>
            <Radio.Group
              defaultValue={'both'}
              onChange={(e) => onChangeOffer({ offerType: e.target.value })}
              value={selectedOfferType}
            >
              <Space direction='vertical'>
                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'deals'}
                >
                  All Deals
                  {/*Both Coupon and Deals*/}
                </Radio>
                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'coupons'}
                >
                  Coupons
                </Radio>

                <Radio
                  className='text-[12px] font-normal text-black dark:text-white'
                  value={'trending'}
                >
                  Trending
                </Radio>

                {/* <Radio
                                    className='text-xs font-normal text-blackWhite'
                                    value={'deals'}
                                >
                                    Deals
                                </Radio> */}
              </Space>
            </Radio.Group>
          </motion.div>
        )}

        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex justify-between items-center mt-[20px] pl-[10px]'
          initial={{ opacity: 0, y: 5 }}
          transition={{ delay: 0.7, duration: 0.3 }}
        >
          <span className='text-blackWhite text-xs font-semibold '>
            Categories
          </span>
          {selectedSubCategories?.length > 0 && (
            <ClearAllBtn
              onClick={() => {
                dispatch(setSelectedSubCategories([]));
                replace(
                  pathname + '?' + createQueryString('subCategories', '')
                );
              }}
            />
          )}
        </motion.div>

        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'
          initial={{ opacity: 0, y: 5 }}
          transition={{ delay: 0.8, duration: 0.3 }}
        >
          {selectedSubCategories.map((item) => (
            <SelectedPill
              key={item.uid}
              onClick={() => {
                const newSelectedUids = selectedSubCategories.filter(
                  (itm) => itm.uid !== item.uid
                );
                dispatch(setSelectedSubCategories(newSelectedUids));
                const newSelectedSubCategoriesString = newSelectedUids
                  .reduce<number[]>((total, item) => total.concat(item.uid), [])
                  .join(',');

                replace(
                  pathname +
                    '?' +
                    createQueryString(
                      'subCategories',
                      newSelectedSubCategoriesString
                    )
                );
              }}
              text={item.name}
            />
          ))}
          <SearchInput
            onChange={(value) => dispatch(setSearchValueCat(value))}
            onClose={() => dispatch(setSearchValueCat(''))}
            rootClass='mt-[10px] !pl-[15px]'
            value={searchValueCat}
          />
        </motion.div>

        <motion.div
          animate={{ opacity: 1, y: 0 }}
          className='mt-[16px] flex gap-y-[10px] flex-col'
          initial={{ opacity: 0, y: 5 }}
          transition={{ delay: 0.9, duration: 0.3 }}
        >
          {filteredCategories.map((item) => (
            <Categories
              createQueryString={createQueryString}
              item={item}
              key={item.uid}
            />
          ))}
        </motion.div>
      </motion.div>
    </motion.div>
  );
};

export const Categories = ({
  item,
  createQueryString,
}: {
  item: AllCategoriesResponse;
  createQueryString: any;
}) => {
  const { selectedSubCategories } = useAppSelector(
    (state) => state.commonFilters
  );
  const dispatch = useAppDispatch();
  const searchParams = useSearchParams();
  const { replace } = useRouter();
  const pathname = usePathname();
  const [isOpen, setOpen] = useState(false);
  const subCategoryHandler = ({ uid, name }: { uid: number; name: string }) => {
    const newSelectedSubCategories = toggleCategory({
      arr: selectedSubCategories,
      uid,
      name,
    });
    dispatch(setSelectedSubCategories(newSelectedSubCategories));

    const newSelectedSubCategoriesString = newSelectedSubCategories
      .reduce<number[]>((total, item) => total.concat(item.uid), [])
      .join(',');

    replace(
      pathname +
        '?' +
        createQueryString('subCategories', newSelectedSubCategoriesString)
    );
  };

  const trendingMainCategory = searchParams.get('trendingMainCategory');
  const isTrendingCategory = trendingMainCategory === item.uid.toString();

  //open the category list if any of the sub-cat item is selectedSubCategories
  //this is useful if user open a shared page with selected sub categories
  useEffect(() => {
    const result = item.subCategories.some((item) =>
      selectedSubCategories.some((ele) => ele.uid === item.uid)
    );
    if (result || isTrendingCategory) {
      setOpen(true);
    }
  }, [selectedSubCategories, item, isTrendingCategory]);

  return (
    <motion.div
      animate={{ opacity: 1, y: 0 }}
      data-category={item.name}
      initial={{ opacity: 0, y: 10 }}
      key={item.uid}
      transition={{ duration: 0.4 }}
    >
      <motion.div
        className='bg-container px-[10px] h-[37px] w-full flex items-center justify-between shadow-sm rounded-[5px] cursor-pointer border-[0.5px] border-[#969696]'
        onClick={() => setOpen(!isOpen)}
        style={{ boxShadow: '0px 0px 10px 0px rgba(0, 0, 0, 0.10)' }}
        transition={{ type: 'spring', stiffness: 400, damping: 17 }}
        whileHover={{ scale: 1.02 }}
        whileTap={{ scale: 0.98 }}
      >
        <span className='text-blackWhite text-[12px]'>{item.name}</span>
        <ArrowDown
          className={clsx(
            isOpen && 'rotate-[180deg]',
            'w-[12px] text-[#969696] transition-transform duration-300'
          )}
        />
      </motion.div>

      <AnimatePresence>
        {isOpen && (
          <motion.div
            animate={{ opacity: 1, height: 'auto' }}
            exit={{ opacity: 0, height: 0 }}
            initial={{ opacity: 0, height: 0 }}
            transition={{ duration: 0.3 }}
          >
            {item?.subCategories?.map((item) => (
              <motion.div
                animate={{ opacity: 1, x: 0 }}
                className='mt-[16px] flex flex-col gap-y-[12px] pl-[10px]'
                initial={{ opacity: 0, x: -5 }}
                key={item.uid}
                transition={{ duration: 0.2 }}
              >
                <Checkbox
                  checked={
                    selectedSubCategories.some((ele) => ele.uid === item.uid) ||
                    isTrendingCategory
                  }
                  onChange={() =>
                    subCategoryHandler({ uid: item.uid, name: item.name })
                  }
                  value={item.uid}
                >
                  <span className='text-blackWhite text-[12px] font-normal'>
                    {item.name}
                  </span>
                </Checkbox>
              </motion.div>
            ))}
          </motion.div>
        )}
      </AnimatePresence>
    </motion.div>
  );
};

export default CommonFilterSidebar;
