'use client';
import Pattern1 from '@/app/components/svg/patterns/pattern1';
import type React from 'react';
import { motion } from 'framer-motion';
import Image from 'next/image';
import Link from 'next/link';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { useState, useEffect } from 'react';

interface ReferFriendBannerProps {
  isLink?: boolean;
}

const ReferFriendBanner: React.FC<ReferFriendBannerProps> = ({ isLink }) => {
  const dispatch = useAppDispatch();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const handleClick = () => {
    // Only access localStorage after hydration
    if (typeof window !== 'undefined') {
      localStorage.setItem('redirectTo', '/referral');
    }
    dispatch(setLoginModalOpen(true));
  };

  // Show loading state during hydration to prevent mismatch
  if (!isHydrated) {
    return <ReferFriendBannerCard />;
  }

  return isLink ? (
    isUserLogin ? (
      <Link className='block w-full' href={'/referral'}>
        <ReferFriendBannerCard />
      </Link>
    ) : (
      <ReferFriendBannerCard onClick={handleClick} />
    )
  ) : (
    <ReferFriendBannerCard />
  );
};

const ReferFriendBannerCard = ({ onClick }: { onClick?: () => void }) => {
  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: { duration: 0.5 },
    },
  };
  return (
    <motion.div
      className='flex-center flex-col lg:flex-row lg:my-[30px]'
      onClick={onClick}
      variants={itemVariants}
      whileHover={{ scale: 1.05 }}
    >
      <Image
        alt='refer friends'
        className='w-[140px] h-auto lg:w-[200px]'
        height={762}
        src={'/img/my-earnings/boy-with-gift.png'}
        title='refer friends'
        width={648}
      />
      <div className='w-full lg:w-auto mt-[12px] lg:mt-0 lg:ml-[20px] xl:ml-[50px]'>
        <h4 className='text-center text-sm lg:text-[22px] font-bold'>
          Refer Your Friends!
        </h4>
        <div className='w-full h-[86px] lg:w-[400px] xl:w-[509px] lg:h-[168px] flex-center rounded-[5px] relative bg-primary mt-[12px]'>
          <Pattern1 className='text-[#8C82E0] absolute top-[12px] left-[12px]' />
          <Pattern1 className='text-[#8C82E0] absolute bottom-[12px] right-[12px]' />
          <Image
            alt='refer friends'
            className='absolute right-[7px] top-[5px] w-[60px] h-auto lg:w-[80px] xl:w-[120px]'
            height={291}
            src={'/img/my-earnings/coin-in-hands.png'}
            title='refer friends'
            width={323}
          />
          <div className='text-[10px] text-center lg:text-[19px] font-medium text-white leading-[1.7]'>
            And Get{' '}
            <span className='text-[23px] ml-[6px] font-black leading-[0]'>
              10%
            </span>
            <br />
            of your Friend's Cashback <br /> for{' '}
            <span className='font-bold'>Lifetime</span>
          </div>
          <p className='text-[8px] absolute bottom-2 left-10 text-white/80 font-medium mt-[5px] lg:mt-[8px]'>
            *T&C Apply
          </p>
        </div>
      </div>
    </motion.div>
  );
};

export default ReferFriendBanner;
