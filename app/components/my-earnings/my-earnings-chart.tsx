'use client';
import React, { useMemo, useState } from 'react';
import Highcharts from 'highcharts/es-modules/masters/highcharts.src'; //import Highcharts as an es-module (fix)
// import 'highcharts/es-modules/masters/modules/exporting.src';

import HighchartsReact from 'highcharts-react-official';
import SlidingButton from '../atoms/sliding-button';

const MyEarningsChart = ({
  monthlyCashback,
  monthlyClicks,
  monthlyOrderAmount,
}: {
  monthlyCashback: number[];
  monthlyClicks: number[];
  monthlyOrderAmount: number[];
}) => {
  const [data, setData] = useState<{ name: string; data: Array<number> }>({
    name: 'Cashback',
    data: monthlyCashback,
  });
  const [selectedTab, setSelectedTab] = useState<number>(1);

  // Generate rolling 12-month categories ending with current month
  const generateMonthCategories = useMemo(() => {
    const monthNames = [
      'Jan',
      'Feb',
      'Mar',
      'Apr',
      'May',
      'Jun',
      'Jul',
      'Aug',
      'Sep',
      'Oct',
      'Nov',
      'Dec',
    ];

    const currentDate = new Date();
    const currentMonth = currentDate.getMonth(); // 0-11
    const currentYear = currentDate.getFullYear();

    const categories: string[] = [];

    // Generate 12 months ending with current month
    for (let i = 11; i >= 0; i--) {
      const targetDate = new Date(currentYear, currentMonth - i, 1);
      const monthIndex = targetDate.getMonth();
      const year = targetDate.getFullYear();
      const yearSuffix = year.toString().slice(-2); // Get last 2 digits of year

      categories.push(`${monthNames[monthIndex]} ${yearSuffix}`);
    }

    return categories;
  }, []);

  const options = useMemo(() => {
    return {
      chart: {
        type: 'spline',
        backgroundColor: '#00000000',
        style: {
          fontSize: '10px',
        },
      },
      accessibility: { enabled: false },
      credits: {
        enabled: false,
      },
      title: {
        text: undefined,
      },
      yAxis: {
        title: false,
        labels: {
          style: {
            color: '#8B8B8B',
          },
        },
      },
      xAxis: {
        categories: generateMonthCategories,
        labels: {
          style: {
            color: '#8B8B8B',
            useHTML: true,
          },
        },
      },
      series: [
        {
          ...data,
          color: '#7366d9',
          showInLegend: false,
        },
      ],
    };
  }, [data, generateMonthCategories, selectedTab]);

  const handlerSwitchData = (option: number) => {
    setSelectedTab(option);
    switch (option) {
      case 1:
        setData({ name: 'Cashback', data: monthlyCashback });
        break;
      case 2:
        setData({ name: 'Click', data: monthlyClicks });
        break;
      case 3:
        setData({ name: 'Amount', data: monthlyOrderAmount });
        break;
    }
  };

  return (
    <div className='mt-[10px] bg-container rounded-[10px] w-full mx-auto'>
      <div className='pt-[14px] mb-[20px] px-[16px] flex justify-between'>
        <h4 className='text-[11px] lg:text-sm font-pat'>Cashback Earnings</h4>
        {/* <button className='w-[54px] lg:w-[98px] h-[35px] text-[10px] lg:text-xs text-blackWhite font-medium flex-center bg-white dark:bg-[#3E424C] rounded-[5px] shadow-sm'>
          Yearly
        </button> */}
      </div>
      <SlidingButton
        buttonDetails={[
          { title: 'Cashback', value: '1' },
          { title: 'Clicks', value: '2' },
          { title: 'Order Amount', value: '3', mobileTitle: 'Amount' },
        ]}
        defaultSelectedBtn={1}
        onChange={(e) => handlerSwitchData(Number(e.target.value))}
        rootClassName='!mt-[18px] !mb-[20px] lg:mt-[21px]'
        uniqueId='earningsChart'
      />

      <HighchartsReact
        containerProps={{ className: 'h-[350px] text-blackWhite' }}
        highcharts={Highcharts}
        key={`chart-${selectedTab}`}
        options={options}
      />
    </div>
  );
};

export default MyEarningsChart;
