'use client';
import {
  Checkbox,
  ConfigProvider,
  DatePicker,
  DatePickerProps,
  MenuProps,
  theme,
} from 'antd';
import clsx from 'clsx';
import React, { useEffect, useRef, useState } from 'react';
import SortSVG from '../svg/sort';
import ToolbarDropdown from '../atoms/toolbar-dropdown';
import { FilterCount } from '../common-toolbar';
import FilterSVG from '../svg/filter';
import DateRangePickerCustom from '../dropdowns/date-range-picker';
import SearchSVG from '../svg/search';
import CrossSVG from '../svg/cross';
import SelectMultipleOptions from '../atoms/select-multiple-options';
import ThemeButton from '../atoms/theme-btn';
import BottomDrawer from '../atoms/BottomDrawer';
import { SortTypes } from '@/services/api/data-contracts';
import { useTheme } from 'next-themes';
import dayjs from 'dayjs';
import PillButton, { SelectedPill } from '../atoms/pills';
import SearchInput from '../atoms/search-input';
import RightArrow from '../svg/right-arrow';
import { useAppSelector } from '@/redux/hooks';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import { useCreateQueryString } from '@/utils/custom-hooks';

const MyEarningsToolbar = ({
  showTitle = true,
  rootClassName,
  selectedSort,
  onClickSortBy,
  onDateChange,
  selectedDate,
  searchKey,
  onApply,
  onClear,
  selectedStatusArray,
  selectedStoreArray,
  selectedTypeArray,
  disableDate = false,
}: {
  showTitle?: boolean;
  rootClassName?: string;
  selectedSort?: SortTypes;
  onClickSortBy?: MenuProps['onClick'];
  selectedDate?: string;
  onDateChange?: DatePickerProps['onChange'];
  searchKey?: string;
  selectedStores?: string[];
  onApply: (options?: {
    selectedStores?: string[];
    selectedStatus?: string[];
    selectedType?: string[];
  }) => void;
  onClear: () => void;
  selectedStoreArray?: string[];
  selectedStatusArray?: string[];
  selectedTypeArray?: string[];
  disableDate?: boolean;
}) => {
  const [searchActive, setSearchActive] = useState(false);
  const [searchOpen, setSearchOpen] = useState(false);
  const [isMobileFilterOpen, setMobileFilterOpen] = useState(false);
  const [isDesktopFilterOpen, setDesktopFilterOpen] = useState(false);

  const [storesNameArray, setStoresNameArray] = useState<string[]>([]);
  const [storeSearchInput, setStoreSearchInput] = useState<string>('');
  const [selectedStores, setSelectedStores] = useState<string[]>([]);
  const [selectedStatus, setSelectedStatus] = useState<string[]>([]);
  const [selectedType, setSelectedType] = useState<string[]>([]);
  
  // Type options for the filter
  const typeList = ['click', 'missing', 'refer and earn', 'share and earn'];
  
  // Mapping from frontend display values to API values
  const typeMapping: { [key: string]: string } = {
    'click': 'click',
    'missing': 'missing',
    'refer and earn': 'referral',
    'share and earn': 'share'
  };
  
  // Reverse mapping from API values to frontend display values
  const reverseTypeMapping: { [key: string]: string } = {
    'click': 'click',
    'missing': 'missing',
    'referral': 'refer and earn',
    'share': 'share and earn'
  };
  const { resolvedTheme } = useTheme();
  const {
    title,
    totalFiltersApplied,
    sortItems,
    hideSearchFilter,
    showStoreFilter,
    hideSortFilter,
    singleDatePicker,
    hideSlidingUI,
    storesList,
    statusList,
  } = useAppSelector((state) => state.earningsToolbar);
  const inputRef = useRef<HTMLInputElement>(null);
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createQueryString = useCreateQueryString(searchParams);

  useEffect(() => {
    setSelectedStores(selectedStoreArray ?? []);
    setSelectedStatus(selectedStatusArray ?? []);
    // Convert API values to frontend display values for types
    const mappedSelectedType = (selectedTypeArray ?? []).map(type => reverseTypeMapping[type] || type);
    setSelectedType(mappedSelectedType);
  }, [selectedStoreArray, selectedStatusArray, selectedTypeArray]);

  const handleFilterModal = () => {
    if (window && window.innerWidth <= 1023) {
      setMobileFilterOpen(!isMobileFilterOpen);
    } else {
      setDesktopFilterOpen(!isDesktopFilterOpen);
    }
  };

  const handleCloseSearch = () => {
    if (inputRef.current) {
      if (inputRef.current.value) {
        inputRef.current.value = '';
        inputRef.current.focus();
        replace(pathname + '?' + createQueryString('searchParam', ''));
      } else {
        setSearchActive(false);
      }
    }
  };

  const handleSearch = (e: React.KeyboardEvent<HTMLInputElement>) => {
    const newSearchValue = inputRef.current?.value;
    if (e.key === 'Enter' && newSearchValue) {
      replace(
        pathname + '?' + createQueryString('searchParam', newSearchValue)
      );
    }
  };

  const handleApply = async () => {
    // Convert frontend display values to API values for types
    const mappedSelectedType = selectedType.map(type => typeMapping[type] || type);
    onApply({ selectedStores, selectedStatus, selectedType: mappedSelectedType });
    handleFilterModal();
  };

  // const handleClickStatusMobileChange = (item: string) => {
  //   const isSelected = selectedStatus.includes(item);
  //   let clickStatus;
  //   if (isSelected) {
  //     clickStatus = selectedStatus.filter((status) => status !== item);
  //   } else {
  //     clickStatus = [...selectedStatus, item];
  //   }
  //   setSelectedStatus(clickStatus);
  //   const hasSelectedStatus = clickStatus.length > 0;

  //   if (!hasSelectedStatus) {
  //     // Clear existing filters
  //     replace(pathname + '?' + createQueryString('status', ''));
  //     return;
  //   }

  //   replace(
  //     pathname + '?' + createQueryString('status', clickStatus.join(','))
  //   );
  // };

  // const handleClickedStoreMobileChange = (item: string) => {
  //   const isSelected = selectedStores.includes(item);
  //   let clickedStoresList;
  //   if (isSelected) {
  //     clickedStoresList = selectedStores.filter((name) => name !== item);
  //   } else {
  //     clickedStoresList = [...selectedStores, item];
  //   }

  //   setSelectedStores(clickedStoresList);
  //   const hasSelectedStores = clickedStoresList.length > 0;

  //   if (!hasSelectedStores) {
  //     // Clear existing filters
  //     replace(pathname + '?' + createQueryString('stores', ''));
  //     return;
  //   }

  //   const selectedStoreUids = hasSelectedStores
  //     ? clickedStoresList.map(
  //         (storeName) =>
  //           storesList
  //             ?.find((store) => store.name === storeName)
  //             ?.uid?.toString() || ''
  //       )
  //     : [];

  //   replace(
  //     pathname + '?' + createQueryString('stores', selectedStoreUids.join(','))
  //   );
  // };

  const handleStatusChange = (input: string) => {
    const isSelected = selectedStatus.includes(input);
    if (isSelected) {
      setSelectedStatus(selectedStatus.filter((status) => status !== input));
    } else {
      setSelectedStatus([...selectedStatus, input]);
    }
  };
  const handleStoreChange = (input: string) => {
    const isSelected = selectedStores.includes(input);
    if (isSelected) {
      setSelectedStores(selectedStores.filter((name) => name !== input));
    } else {
      setSelectedStores([...selectedStores, input]);
    }
  };
  
  const handleTypeChange = (input: string) => {
    const isSelected = selectedType.includes(input);
    if (isSelected) {
      setSelectedType(selectedType.filter((type) => type !== input));
    } else {
      setSelectedType([...selectedType, input]);
    }
  };

  const handleClear = () => {
    setSelectedStatus([]);
    setSelectedStores([]);
    setSelectedType([]);
    onClear();
    handleFilterModal();
  };

  //stores searching in dropdown list
  useEffect(() => {
    let filteredStoreNamesArray;
    if (storeSearchInput.trim() !== '') {
      filteredStoreNamesArray =
        storesList &&
        storesList
          .filter((store) =>
            store.name.toLowerCase().includes(storeSearchInput.toLowerCase())
          )
          .map((store) => store.name);
    } else {
      filteredStoreNamesArray =
        storesList && storesList.map((store) => store.name);
    }
    setStoresNameArray(filteredStoreNamesArray ?? []);
  }, [storesList, storeSearchInput]);

  const currentDate = dayjs(); // Get the current date
  const disabledDateFunc = (date: any) => {
    // If the current date is the 5th or later
    if (currentDate.date() >= 5) {
      // Disable all dates from the previous month
      return date.isBefore(currentDate.startOf('month'));
    }
    return false; // No dates are disabled otherwise
  };

  return (
    <>
      {/* =========================Desktop View========================= */}
      <div className='sticky top-[64px] lg:top-[146px] z-[2]'>
        <div
          className={clsx(
            rootClassName,
            'min-h-[56px] flex items-center justify-center lg:justify-between gap-2 w-full bg-[#E1E2E4] dark:bg-[#20222a] px-[8px] sm:px-4 lg:px-[20px] relative z-[2]'
          )}
          onClick={(e) => {
            e.stopPropagation();
            e.preventDefault();
          }}
        >
          <div className='hidden lg:inline-block'>
            {showTitle && (
              <h4 className='text-sm font-pat text-blackWhite'>{title}</h4>
            )}
          </div>
          <div className='flex gap-2 grow justify-evenly lg:justify-end'>
            {!hideSortFilter && (
              <ToolbarDropdown
                className={clsx(searchActive && 'hidden md:flex')}
                items={sortItems}
                name='Sort'
                onClick={onClickSortBy}
              >
                <>
                  <SortSVG className='w-[14px] lg:w-[20px] text-[#7366D9] dark:text-white' />
                  {selectedSort && (
                    <FilterCount
                      char={(selectedSort?.charAt(0) || '').toUpperCase()}
                    />
                  )}
                </>
              </ToolbarDropdown>
            )}

            <button
              className={clsx(
                searchActive && 'hidden md:flex',
                (isDesktopFilterOpen || isMobileFilterOpen) &&
                  '!bg-primary !text-white',
                'relative flex bg-white dark:bg-[#515662] h-[40px] w-[65px] lg:w-[80px] rounded-[5px] text-[12px] text-[#1C132E] dark:text-white font-medium lg:font-semibold justify-evenly items-center '
              )}
              onClick={handleFilterModal}
              style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
            >
              <FilterSVG
                className={clsx(
                  (isDesktopFilterOpen || isMobileFilterOpen) && '!text-white',
                  'w-[10px] lg:w-[15px] text-[#7366D9] dark:text-white'
                )}
              />
              <span>Filter</span>
              {totalFiltersApplied && totalFiltersApplied > 0 ? (
                <FilterCount totalFilters={totalFiltersApplied} />
              ) : null}
            </button>

            {singleDatePicker ? (
              <ConfigProvider
                theme={{
                  algorithm:
                    resolvedTheme === 'dark'
                      ? theme.darkAlgorithm
                      : theme.defaultAlgorithm,
                }}
              >
                <DatePicker
                  className='dark:bg-[#515662]'
                  format={'DD-MM-YYYY'}
                  inputReadOnly
                  onChange={onDateChange}
                  size='middle'
                  style={{
                    width: 'w-[65px]',
                    height: '40px',
                    fontStyle: 'font-bold',
                    boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)',
                  }}
                  value={selectedDate ? dayjs(selectedDate) : undefined}
                  {...(disableDate && { disabledDate: disabledDateFunc })}
                />
              </ConfigProvider>
            ) : (
              <DateRangePickerCustom
                rootClassName={clsx(searchActive && 'hidden lg:flex')}
              />
            )}

            {!hideSearchFilter && (
              <div
                className={clsx(
                  searchActive && '!w-full',
                  'flex items-center justify-end w-[45px] lg:w-[269px] md:max-w-[269px] h-[40px] bg-white dark:bg-[#18191D] rounded-[5px] px-4 cursor-pointer relative z-[501] shrink-0'
                )}
                onClick={() => setSearchActive(true)}
                style={{ boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)' }}
              >
                <input
                  className='placeholder:text-[#AFAFAF] h-full w-full outline-none bg-transparent dark:text-white text-[12px]'
                  defaultValue={searchKey}
                  onBlur={() => {
                    if (inputRef.current?.value === '') {
                      setSearchActive(false);
                      replace(
                        pathname + '?' + createQueryString('searchParam', '')
                      );
                    }
                  }}
                  onFocus={() => setSearchActive(true)}
                  onKeyDown={(e) => handleSearch(e)}
                  placeholder={'search...'}
                  ref={inputRef}
                  type='text'
                />
                {!searchActive ? (
                  <div className=' h-full flex justify-center items-center'>
                    <SearchSVG className='w-[14px] lg:w-[18px] text-[#7366D9] dark:text-white' />
                  </div>
                ) : (
                  <div
                    className='h-full flex items-center justify-center pl-2'
                    onClick={() => {
                      handleCloseSearch();
                    }}
                  >
                    <CrossSVG className='w-[10px] lg:w-[12px] text-[#7366D9] dark:text-white cursor-pointer' />
                  </div>
                )}
              </div>
            )}
          </div>
        </div>
        {!hideSlidingUI && isDesktopFilterOpen && (
          <div
            className={clsx(
              isDesktopFilterOpen ? 'top-[55px]' : 'top-[-17px]',
              'hidden lg:flex lg:items-center lg:justify-between px-[20px] xl:px-[30px] h-[70px] absolute w-full bg-[#E1E2E4] dark:bg-[#2D313A] shadow transition-all'
            )}
          >
            <div className='flex gap-x-[10px]'>
              {showStoreFilter && (
                <SelectMultipleOptions
                  data={storesNameArray}
                  isSearchable
                  onChangeSearch={(input: string) => {
                    setStoreSearchInput(input);
                  }}
                  onClose={() => {
                    setStoreSearchInput('');
                  }}
                  onSelectItem={(input: string) => handleStoreChange(input)}
                  placeholder='Select Stores'
                  searchValue={storeSearchInput}
                  selectedItems={selectedStores}
                />
              )}
              <SelectMultipleOptions
                data={statusList}
                isSearchable={false}
                onSelectItem={(input: string) => handleStatusChange(input)}
                placeholder='Select Status'
                selectedItems={selectedStatus}
              />
              <SelectMultipleOptions
                data={typeList}
                isSearchable={false}
                onSelectItem={(input: string) => handleTypeChange(input)}
                placeholder='Select Type'
                selectedItems={selectedType}
              />
            </div>
            <div className='flex gap-x-[20px]'>
              <ThemeButton
                className='!w-[99px]'
                onClick={() => handleApply()}
                text='APPLY'
              />
              <button
                className='text-[10px] font-medium text-[#404040] dark:text-white'
                onClick={() => handleClear()}
              >
                Clear All
              </button>
            </div>
          </div>
        )}
      </div>

      {/* ===========================Mobile View==================== */}
      <BottomDrawer
        heightClass='75svh'
        onClose={() => setMobileFilterOpen(false)}
        open={isMobileFilterOpen}
        title='Filter'
        titleIcon={<></>}
        topClass='calc(100% - 75svh)'
      >
        {/* <div>This will be filter modal for mobile and tablets.</div> */}

        <div onClick={(e) => e.preventDefault()}>
          <h4 className='mt-[30px] mb-[20px] text-blackWhite text-xs font-semibold pl-[8px]'>
            Select Status
          </h4>
          <div className='flex flex-col gap-y-[15px] items-start pl-[5px] max-h-[250px] overflow-y-auto mt-[15px] pb-[10px] customScrollbar'>
            {statusList.map((item, index) => {
              const isSelected =
                selectedStatus && selectedStatus.includes(item);
              return (
                <label
                  className='cursor-pointer'
                  key={index + 1}
                  onClick={() => handleStatusChange(item)}
                >
                  <Checkbox checked={isSelected} key={index + 1} />
                  <span className='text-[#404040] dark:text-white text-xs font-medium ml-[10px] lg:ml-[15px]'>
                    {item}
                  </span>
                </label>
              );
            })}
          </div>

          <h4 className='mt-[30px] mb-[20px] text-blackWhite text-xs font-semibold pl-[8px]'>
            Select Type
          </h4>
          <div className='flex flex-col gap-y-[15px] items-start pl-[5px] max-h-[250px] overflow-y-auto mt-[15px] pb-[10px] customScrollbar'>
            {typeList.map((item, index) => {
              const isSelected =
                selectedType && selectedType.includes(item);
              return (
                <label
                  className='cursor-pointer'
                  key={`type-${index + 1}`}
                  onClick={() => handleTypeChange(item)}
                >
                  <Checkbox checked={isSelected} key={`type-${index + 1}`} />
                  <span className='text-[#404040] dark:text-white text-xs font-medium ml-[10px] lg:ml-[15px]'>
                    {item}
                  </span>
                </label>
              );
            })}
          </div>

          {selectedStores && selectedStores.length > 0 && (
            <>
              <h4 className='text-[10px] text-blackWhite mt-[20px]'>
                Selected Stores
              </h4>
              <div className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'>
                {selectedStores.map((item, index) => (
                  <SelectedPill
                    key={index}
                    onClick={() => {
                      handleStoreChange(item);
                    }}
                    text={item}
                  />
                ))}
              </div>
            </>
          )}
          
          {selectedType && selectedType.length > 0 && (
            <>
              <h4 className='text-[10px] text-blackWhite mt-[20px]'>
                Selected Types
              </h4>
              <div className='flex flex-wrap mt-[13px] gap-y-[8px] gap-x-[10px]'>
                {selectedType.map((item, index) => (
                  <SelectedPill
                    key={`selected-type-${index}`}
                    onClick={() => {
                      handleTypeChange(item);
                    }}
                    text={item}
                  />
                ))}
              </div>
            </>
          )}
          <>
            {/* ---------------Stores------------- */}

            {showStoreFilter &&
              storesNameArray.length > 0 &&
              (searchOpen ? (
                <div className='flex items-center '>
                  <RightArrow
                    className='w-[16px] rotate-[180deg] text-primary mr-[15px] mt-5'
                    onClick={() => setSearchOpen(false)}
                  />
                  <SearchInput
                    onChange={(input: string) => {
                      setStoreSearchInput(input);
                    }}
                    onClose={() => {
                      setStoreSearchInput('');
                    }}
                    rootClass='!w-full border border-primary mt-5'
                    value={storeSearchInput}
                  />
                </div>
              ) : (
                <div className='flex items-center mt-[20px]'>
                  <h4 className='text-[10px] text-blackWhite2 font-semibold'>
                    Select Stores
                  </h4>

                  <div className='grow h-[0.5px] bg-[#C2BAFF] mx-[10px]' />
                  <SearchSVG
                    className='text-[14px] text-primary'
                    onClick={() => {
                      setSearchOpen(true);
                    }}
                  />
                </div>
              ))}
          </>
          {/* )} */}

          <div className='flex flex-wrap mt-[30px] overflow-hidden pb-[3px] gap-x-[8px] gap-y-[10px]'>
            <>
              {/* -----------------------Search Results--------------- */}
              {showStoreFilter &&
                storesNameArray &&
                storesNameArray.map((item, index) => (
                  <PillButton
                    className='border-[1px] border-primary dark:text-white w-max'
                    isSelected={selectedStores.some((itm) => itm === item)}
                    key={index}
                    onClick={() => {
                      handleStoreChange(item);
                    }}
                    text={item}
                  />
                ))}
            </>
          </div>

          <div className='sticky bottom-[-1px] h-[60px]  bg-container flex-center gap-x-[30px] mt-[40px] z-[9] w-full'>
            <ThemeButton
              className='!w-[99px] !text-[12px]'
              onClick={() => handleApply()}
              text='APPLY'
            />
            <ThemeButton
              className='!w-[100px] !text-[12px]'
              onClick={handleClear}
              text='Reset All'
            />
          </div>
        </div>
      </BottomDrawer>
    </>
  );
};
export default MyEarningsToolbar;
