'use client';
import { ArrowRightIcon, Copy, Share2 } from 'lucide-react';
import Image from 'next/image';
import React, { useEffect, useState } from 'react';
import { useDispatch } from 'react-redux';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { useAppSelector } from '@/redux/hooks';
import { APP_URL } from '@/config';
import { copyToClipboard } from '@/utils/helpers';
import { toast } from 'react-toastify';
import { RWebShare } from 'react-web-share';
import TelegramSVG from '../svg/social/telegram';
import WhatsappSVG from '../svg/social/whatsapp';
import Link from 'next/link';

const ReferFriendHeroSection = () => {
  const dispatch = useDispatch();
  const { isUserLogin, userDetails } = useAppSelector((state) => state.auth);
  const [isMounted, setIsMounted] = useState(false);

  // Prevent hydration mismatch by only rendering auth-dependent content after mounting
  useEffect(() => {
    setIsMounted(true);
  }, []);

  const shareUrl = `https://${APP_URL}?r=${userDetails?.referralCode || ''}`;

  const handleStartReferring = () => {
    if (!isUserLogin) {
      dispatch(setLoginModalOpen(true));
    }
  };

  const copyUrl = () => {
    copyToClipboard(shareUrl);
    toast.success('URL Copied to Clipboard');
  };

  const openWhatsApp = () => {
    const message = encodeURIComponent(
      `Love getting cashback on your purchases? Me too! 🙌 Join Indian Cashback with my referral link and start earning. It's a win-win! 😉 👉 ${shareUrl}`
    );
    window.open(`https://wa.me/?text=${message}`, '_blank');
  };

  const openTelegram = () => {
    const message = encodeURIComponent(
      `Love getting cashback on your purchases? Me too! 🙌 Join Indian Cashback with my referral link and start earning. It's a win-win! 😉 👉 ${shareUrl}`
    );
    window.open(
      `https://t.me/share/url?url=${shareUrl}&text=${message}`,
      '_blank'
    );
  };

  return (
    <section
      className={
        'w-full min-h-[350px] bg-gradient-to-b from-[#4D40B9] to-[#9D90FF] flex flex-col md:flex-row items-center justify-center px-4 md:px-8 py-6 rounded-lg shadow-lg'
      }
      data-component='refer-friend-hero'
      key='refer-friend-hero-section'
    >
      {/* Hero content goes here */}
      <div className='text-white text-center py-8 md:py-16 w-full md:w-1/2 h-full flex flex-col items-center justify-end gap-y-3 order-2 md:order-1'>
        <Image
          alt='referral hero'
          className='w-24 md:w-32 h-24 md:h-32 object-cover'
          height={300}
          src={'/temp/referrals/coin.png'}
          width={300}
        />
        <h1 className='text-2xl md:text-3xl lg:text-5xl font-medium mb-2 md:mb-4'>
          Refer & Earn with IndianCashback
        </h1>
        <p className='text-sm md:text-base lg:text-xl mb-4 max-w-[568px]'>
          Invite Friends. Earn Rewards. Win Top Spots On The Leaderboard!
        </p>

        {/* Only render conditional content after component has mounted */}
        {!isMounted ? (
          // Show loading state or default button during hydration
          <button
            className='bg-[#FFC554] text-black font-medium py-2 rounded shadow hover:bg-[#FFC554]/80 hover:scale-105 transition text-base px-6 flex items-center justify-center gap-x-4'
            onClick={handleStartReferring}
            type='button'
          >
            Start Referring Now
            <ArrowRightIcon className='w-4 h-4' />
          </button>
        ) : isUserLogin && userDetails?.referralCode ? (
          // Logged in user with referral code - show invite friends interface
          <div className='w-full max-w-md bg-white rounded-lg p-1 '>
            <div className='text-black border-b border-gray-200 flex items-center justify-between px-4 py-2 mb-1 bg-gray-50 rounded-t-lg'>
              <h3 className='text-base font-medium'>Invite Friends</h3>
              <Link
                className='text-[#4D40B9] text-xs font-medium hover:text-primary hover:underline transition-all duration-300'
                href='/referral-history'
              >
                See My Referral History
              </Link>
            </div>
            <div className='flex items-center justify-between gap-x-1 md:gap-x-2 px-4 py-2'>
              <div className='flex justify-start items-center relative w-[60%] min-w-[100px] sm:min-w-[150px] lg:min-w-[250px] border border-gray-200 rounded-lg'>
                <input
                  className='w-[calc(100%-20px)] truncate px-3 py-2 text-black text-sm bg-transparent border-none outline-none'
                  readOnly
                  type='text'
                  value={shareUrl}
                />
                <button
                  className='absolute right-1 top-1 p-1 hover:bg-gray-100 rounded transition-colors cursor-pointer'
                  onClick={copyUrl}
                  title='Copy Link'
                >
                  <Copy className='w-5 h-5 text-gray-600' />
                </button>
              </div>
              <RWebShare
                data={{
                  text: `Love getting cashback on your purchases? Me too! 🙌 Join Indian Cashback with my referral link and start earning. It's a win-win! 😉 👉 `,
                  url: shareUrl,
                  title: 'Share your referral link with friends!',
                }}
              >
                <button
                  className='p-1 lg:p-2 hover:bg-gray-100 rounded transition-colors'
                  title='Share'
                >
                  <Share2 className='w-5 h-5 text-gray-600' />
                </button>
              </RWebShare>
              <button
                className='p-1 lg:p-2 hover:bg-blue-50 rounded transition-colors'
                onClick={openTelegram}
                title='Share on Telegram'
              >
                <TelegramSVG className='text-[#40B3E0] w-[26px]' />
              </button>
              <button
                className='p-1 lg:p-2 hover:bg-green-50 rounded transition-colors'
                onClick={openWhatsApp}
                title='Share on WhatsApp'
              >
                <WhatsappSVG className='text-green-500 w-[26px]' />
              </button>
            </div>
          </div>
        ) : (
          // Not logged in or no referral code - show start referring button
          <button
            className='bg-[#FFC554] text-black font-medium py-2 rounded shadow hover:bg-[#FFC554]/80 hover:scale-105 transition text-base px-6 flex items-center justify-center gap-x-4'
            onClick={handleStartReferring}
            type='button'
          >
            Start Referring Now
            <ArrowRightIcon className='w-4 h-4' />
          </button>
        )}
      </div>
      <div className='hidden md:block w-full md:w-1/2 order-1 md:order-2'>
        <Image
          alt='referral hero'
          className='w-full h-auto object-cover'
          height={600}
          src={'/temp/referrals/refer-cover.webp'}
          width={600}
        />
      </div>
    </section>
  );
};

export default ReferFriendHeroSection;
