'use client';
import type React from 'react';
import Image from 'next/image';
import { BronzeBadge, SilverBadge, GoldBadge } from './Badges';
import { GetReferralLeaderboardResponse } from '@/services/api/data-contracts';

interface ReferrersTableProps {
  referrers: GetReferralLeaderboardResponse[];
}

const ReferrersTable: React.FC<ReferrersTableProps> = ({ referrers }) => {
  return (
    <div className='relative overflow-x-auto w-full max-w-6xl rounded-lg shadow-lg'>
      <table className='min-w-full bg-white dark:bg-[#212327]'>
        <thead className='table-header-group'>
          <tr className='text-xs border-b border-gray-200 dark:border-gray-700'>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[100px] sticky left-0 z-10'>
              Rank
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-left w-fit min-w-[120px]'>
              User
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] min-w-[120px]'>
              Total Referrals
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-right min-w-[140px]'>
              Cashback Earned
            </th>
            <th className='py-3 px-4 !font-medium bg-white dark:bg-[#18191d] text-center min-w-[150px]'>
              Badge
            </th>
          </tr>
        </thead>
        <tbody className='text-sm'>
          {referrers.map((referrer, index) => (
            <tr
              className='table-row border-b border-gray-200 dark:border-gray-700 text-center'
              key={index}
            >
              <td className='py-2 h-[55px] px-4 text-center border-b-0 table-cell before:font-bold before:content-none bg-[#EEEEEE] dark:bg-[#212327] sticky left-0 z-10'>
                <span className='ml-0 text-center whitespace-nowrap'>
                  {index + 1}
                </span>
              </td>
              <td className='items-center justify-start py-2 h-[55px] px-4 text-left border-b-0 table-cell before:font-bold before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <div className='flex flex-row items-center justify-start gap-x-2'>
                  <Image
                    alt='profile'
                    className='w-[30px] h-[30px] rounded-full'
                    height={30}
                    src={
                      referrer?.name
                        ? `https://ui-avatars.com/api/?name=${referrer.name}&background=random&rounded=true&format=png`
                        : '/temp/profile.png'
                    }
                    width={30}
                  />
                  <span className='ml-0 whitespace-nowrap'>
                    {referrer.name}
                  </span>
                </div>
              </td>
              <td className='py-2 h-[55px] px-4 text-center border-b-0 table-cell before:font-bold before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 whitespace-nowrap'>
                  {referrer.referralCount}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 text-right border-b-0 table-cell before:font-bold before:content-none bg-[#EEEEEE] dark:bg-[#212327]'>
                <span className='ml-0 whitespace-nowrap'>
                  ₹{referrer.totalReferralCommission}
                </span>
              </td>
              <td className='py-2 h-[55px] px-4 border-b-0 table-cell before:font-bold before:content-none bg-[#EEEEEE] dark:bg-[#212327] min-w-[20%]'>
                {index === 0 && <GoldBadge />}
                {index === 1 && 'silver' && <SilverBadge />}
                {index === 2 && 'bronze' && <BronzeBadge />}
              </td>
            </tr>
          ))}
        </tbody>
      </table>
    </div>
  );
};

export default ReferrersTable;
