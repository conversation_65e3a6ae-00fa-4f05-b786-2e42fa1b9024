'use client';

import React from 'react';
import Image from 'next/image';

const SupportedStores = () => {
  const stores = [
    {
      name: '<PERSON>lipkart',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/flipkart-cashback-and-coupon-offers-15030784.png',
      offering: 'Up to 7% Reward Points',
    },
    {
      name: 'Aji<PERSON>',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/ajio-cashback-and-coupon-offers-24620376.jpeg',
      offering: 'Up to 6.5% Cashback',
    },
    {
      name: 'Mynt<PERSON>',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/myntra--cashback-and-coupon-offers-33590913.png',
      offering: 'Up to 6.9% Cashback',
    },
    {
      name: 'Shopsy',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/shopsy-cashback-and-coupon-offers-10427947.png',
      offering: 'Up to 8% Cashback',
    },
    {
      name: 'Croma',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/croma-cashback-and-coupon-offers-11492735.png',
      offering: 'Up to 3.5% Cashback',
    },
    {
      name: 'Tata Cliq',
      logo: 'https://s3-ap-southeast-1.amazonaws.com/icb-images/storeLogos/tata-cliq-cashback-and-coupon-offers-40531419.jpeg',
      offering: 'Flat 4% Cashback',
    },
  ];

  // Create multiple sets for seamless infinite scroll
  const scrollStores = [...stores, ...stores, ...stores];

  return (
    <div className='w-full flex justify-center mb-[5vh]'>
      <div className='w-full max-w-2xl bg-white rounded-lg shadow-sm border border-gray-100 p-6'>
        <h2 className='text-xl font-semibold text-gray-800 mb-4 text-center'>
          Supported Stores
        </h2>

        <div className='relative overflow-hidden'>
          <div
            className='flex animate-scroll space-x-8'
            onMouseEnter={(e) => {
              e.currentTarget.style.animationPlayState = 'paused';
            }}
            onMouseLeave={(e) => {
              e.currentTarget.style.animationPlayState = 'running';
            }}
          >
            {scrollStores.map((store, index) => (
              <div
                className='flex-shrink-0 flex flex-col items-center bg-white rounded-lg border border-gray-200 p-2 shadow-sm hover:shadow-md transition-shadow duration-200'
                key={`${store.name}-${index}`}
              >
                <div className='w-20 h-20 relative'>
                  <Image
                    alt={`${store.name} logo`}
                    className='object-contain p-2'
                    fill
                    sizes='(max-width: 768px) 80px, 80px'
                    src={store.logo}
                  />
                </div>
                <p className='text-xs text-[#574abe] text-center mt-1 font-medium'>
                  {store.offering}
                </p>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  );
};

export default SupportedStores;
