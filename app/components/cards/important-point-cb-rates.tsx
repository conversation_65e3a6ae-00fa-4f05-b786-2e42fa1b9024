import Image from 'next/image';
import React from 'react';

const ImportantPointCard = ({
  imgUrl,
  imgCaption,
  title,
}: {
  imgUrl: string;
  imgCaption: any;
  title: string;
}) => {
  return (
    <div className='min-h-[171px] h-full w-full max-w-[211px] justify-self-center min-w-[150px] md:min-w-[211px] shrink-0 flex flex-col rounded-[5px] overflow-hidden shadow'>
      <div className='grow relative flex flex-col pt-[10px] gap-y-[12px] bg-white dark:bg-[#32363F] items-center justify-center w-full'>
        <Image
          alt='illustration'
          className='max-w-[95px] max-h-[95px]'
          height={87}
          src={imgUrl}
          width={95}
        />
        <span className='text-blackWhite font-black font-nexa'>
          {title === 'Missing CB Acceptence'
            ? imgCaption === true
              ? 'Accepted'
              : 'Not Accepted'
            : imgCaption}
        </span>
      </div>
      <div className='bg-[#FFC554] h-[31px] px-4 lg:h-[55px] text-black text-[8px] sm:text-[9px] lg:text-xs font-semibold flex items-center justify-center text-center w-full'>
        {title}
      </div>
    </div>
  );
};

export default ImportantPointCard;
