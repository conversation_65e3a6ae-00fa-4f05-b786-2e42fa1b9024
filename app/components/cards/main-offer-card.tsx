'use client';
import Image from 'next/image';
import React, { useEffect, useRef, useState } from 'react';
import offerBadge from '@/public/svg/offer-badge.svg';
import cardMenuSVG from '@/public/svg/card-menu-btn.svg';
import clsx from 'clsx';
import { formatStoreName, generateProductUrl } from '@/utils/helpers';
import OfferCardMenu from '../dropdowns/offer-card-menu';
import { useRouter } from 'next/navigation';
import { toast } from 'react-toastify';
import type { OfferCardProps } from '@/types/global-types';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { setShowLeftPanel } from '@/redux/slices/main-header-slice';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { toggleSavedState } from '@/redux/slices/offer-slice';
import type {
  RemoveOfferDto,
  SaveOfferDto,
} from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { useOnClickOutside } from 'usehooks-ts';
import ShimmerEffect, { RectShimmer } from '../atoms/shimmer-effect';
import SmartLink from '../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { Modal } from 'antd';
import CrossSVG from '../svg/cross';
import ThemeButton from '../atoms/theme-btn';
import OfferCountdown from '../common/offer-countdown';

const MainOfferCard = ({
  uid,
  storeName,
  offerTitle,
  productImgUrl,
  storeImgUrl,
  isFromSavedScreen = false,
  isOfferUpto,
  duration,
  children,
  showNewBadge = false,
  rootClass,
  saved = false,
  hideCbTag = false,
  isAutoGenerated = false,
  isLoading = false,
  storePopUpWarning,
}: OfferCardProps & { isLoading?: boolean; storePopUpWarning?: string }) => {
  const extractedChildren =
    React.Children.map(children, (child) => {
      return child;
    }) || [];
  const [showMenuDropdown, setShowMenuDropdown] = useState(false);
  const [isSaved, setIsSaved] = useState(false);
  const isOfferSaved = useAppSelector(
    (state) => state.offer.offersSavedStates[uid]
  );
  const [showWarningModal, setShowWarningModal] = useState(false);

  const dispatch = useAppDispatch();
  const router = useRouter();
  const { isUserLogin } = useAppSelector((state) => state.auth);
  const mainOfferCardRef = useRef<HTMLAnchorElement>(null);

  const url = generateProductUrl(storeName, offerTitle);

  const handleSaveOffer = async () => {
    try {
      if (isSaved) {
        const body: RemoveOfferDto = {
          itemUid: Number(uid),
        };
        await fetchWrapper('/api/proxy/offers/remove', {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });

        dispatch(
          toggleSavedState({
            uid,
            saved: false,
          })
        );

        toast.info('Offer has been removed successfully.');
      } else {
        const body: SaveOfferDto = {
          itemUid: Number(uid),
        };
        await fetchWrapper('/api/proxy/offers/save', {
          method: 'POST',
          body: JSON.stringify(body),
          suppressToast: true,
        });

        dispatch(
          toggleSavedState({
            uid,
            saved: true,
          })
        );

        toast.info('Offer has been saved successfully.');
      }
    } catch (e: any) {
      const message =
        e?.message ?? 'Save offer failed. Please try again later.';
      toast.error(message);
    }
  };

  const handleMenuItemClick = (_key: number, value: string) => {
    if (value === 'Info') {
      router.push(`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`);
    } else if (value === 'Save') {
      if (isUserLogin) {
        handleSaveOffer();
      } else {
        dispatch(setShowLeftPanel(false));
        dispatch(setLoginModalOpen(true));
      }
    }
  };

  useEffect(() => {
    //if isOfferSaved is there we take value from it otherwise goes with the props
    setIsSaved(isOfferSaved ? true : isOfferSaved === false ? false : !!saved);
  }, [saved, isOfferSaved]);

  useOnClickOutside(mainOfferCardRef, () => setShowMenuDropdown(false));

  const handleCardClick = (e?: React.MouseEvent<Element, MouseEvent>) => {
    if (storePopUpWarning && e) {
      e.preventDefault();
      setShowWarningModal(true);
    }
  };

  if (isLoading) {
    return (
      <div
        className={clsx(
          rootClass,
          'mainOfferCard flex flex-col drop-shadow-md lg:w-[192px] shrink-0'
        )}
      >
        <div className='offerImgCont h-[157px] shrink-0'>
          <div className='relative w-auto h-[157px] overflow-hidden rounded-t-[6px]'>
            <ShimmerEffect className='w-full h-full' />
            {/* Add placeholder for menu button to maintain layout */}
            <div className='absolute top-[2px] right-[2px] drop-shadow-lg z-[9] w-[35px] h-[35px]'>
              <RectShimmer height='35px' width='35px' />
            </div>
          </div>
        </div>

        <div className='offerDetailsWrapper flex flex-col grow relative min-h-[152px] rounded-b-[6px] bg-mainCard'>
          <div className='offerStoreCont absolute top-[-16px] left-[50%] translate-x-[-50%]'>
            <div className='relative w-[100px] h-[28px] shrink-0 rounded-[6px] bg-[#FDFDFE] shadow-md overflow-hidden'>
              <ShimmerEffect className='w-full h-full' />
            </div>
          </div>

          <div className='offerEndInCont text-[10px] text-[#FF3737] flex items-center justify-center mt-[25px]'>
            <RectShimmer height='12px' width='80px' />
          </div>

          <div
            className='pt-[5px] text-center text-[10px] font-normal text-[#292B31] dark:text-[#FDFDFE] px-[8px] flex items-center justify-center grow'
            style={{ overflowWrap: 'break-word' }}
          >
            <RectShimmer className='mt-[10px]' height='14px' width='120px' />
          </div>

          <div
            className='mt-[10px] text-center px-[8px] pb-[10px]'
            style={{ overflowWrap: 'break-word' }}
          >
            <RectShimmer className='mx-auto' height='14px' width='100px' />
          </div>

          {isOfferUpto && !hideCbTag && (
            <div className='h-[27px] w-full flex items-center justify-center bg-[#FFC554]'>
              <RectShimmer height='14px' width='80px' />
            </div>
          )}

          <div className='h-[45px] lg:h-[40px] w-full rounded-b-[6px]'>
            <RectShimmer className='w-full h-full' height='45px' width='100%' />
          </div>
        </div>
      </div>
    );
  }

  if (!isSaved && isFromSavedScreen) {
    return null;
  }
  const handleContinueShopping = () => {
    setShowWarningModal(false);
    // Route to the offer page
    router.push(`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`);
  };

  return (
    <>
      <SmartLink
        className={clsx(
          rootClass,
          'mainOfferCard flex flex-col drop-shadow-md lg:w-[192px] shrink-0 cursor-pointer transition-all duration-300 hover:shadow-lg active:scale-[0.97] overflow-hidden group'
        )}
        href={`/store/${formatStoreName(storeName)}/${url}?uid=${uid}`}
        linkType={LinkType.INTERNAL}
        onClick={handleCardClick}
        ref={mainOfferCardRef}
      >
        <div className='offerImgCont shrink-0 rounded-t-[6px] overflow-hidden'>
          <div className='relative w-full  h-full overflow-hidden max-h-[200px] aspect-square bg-white transition-all duration-300 group-hover:scale-110'>
            <Image
              alt='offer product img'
              className={
                isAutoGenerated
                  ? 'object-contain'
                  : 'object-cover w-full h-full'
              }
              fill
              src={productImgUrl}
            />
          </div>
          {showNewBadge && <OfferNewBadge />}
          <div
            className='absolute top-[2px] right-[2px] drop-shadow-lg group z-[9]'
            onClick={(e) => {
              e.stopPropagation();
              e.preventDefault();
            }}
            onKeyDown={(e) => {
              if (e.key === 'Enter') {
                //TODO -  check if it's needed/ temp solution for error
                // Handle keyboard interaction (e.g., triggering the same action as onClick)
                e.stopPropagation();
                e.preventDefault();
              }
            }}
            onKeyUp={(e) => {
              if (e.key === 'Enter') {
                // Handle keyboard interaction (e.g., triggering the same action as onClick)
                e.stopPropagation();
                e.preventDefault();
              }
            }}
          >
            <Image
              alt='card menu'
              height={35}
              onClick={(e) => {
                e.stopPropagation();
                e.preventDefault();
                setShowMenuDropdown(!showMenuDropdown);
              }}
              src={cardMenuSVG}
              width={35}
            />
            <OfferCardMenu
              cardType='offer'
              onClickItem={(key: number, value: string) => {
                handleMenuItemClick(key, value);
                setTimeout(() => setShowMenuDropdown(false), 200);
              }}
              rootClass={clsx(showMenuDropdown ? 'block' : 'hidden')}
              saved={isSaved}
              shareUrl={`/store/${storeName}/${url}?uid=${uid}`}
            />
          </div>
        </div>

        <div className='offerDetailsWrapper flex flex-col grow relative min-h-[152px] rounded-b-[6px] bg-mainCard'>
          <div className='offerStoreCont absolute top-[-16px] left-[50%] translate-x-[-50%]'>
            {!isAutoGenerated ? (
              <div className='relative w-28 h-8 shrink-0 rounded-md bg-[#FDFDFE] shadow-md overflow-hidden'>
                <Image
                  alt={'store img'}
                  className='object-contain scale-[80%]'
                  fill
                  src={storeImgUrl || ''}
                />
              </div>
            ) : (
              <div />
            )}
          </div>

          <div className='flex flex-col justify-between items-center w-full h-full'>
            <div className='flex flex-col justify-start items-center w-full'>
              <OfferCountdown duration={duration} />

              <div
                className='pt-[5px] text-center text-[10px] xl:text-[11px] font-normal text-[#292B31] dark:text-[#FDFDFE] px-[8px] flex items-center justify-center grow'
                style={{ overflowWrap: 'break-word' }}
              >
                {extractedChildren[0]}
              </div>
            </div>
            <div className='flex flex-col justify-end items-center w-full'>
              <div
                className='mt-[10px] text-center px-[8px] pb-[10px]'
                style={{ overflowWrap: 'break-word' }}
              >
                {extractedChildren[1]}
              </div>
              {isOfferUpto && !hideCbTag && (
                <div className='h-[27px] w-full flex items-center justify-center bg-[#FFC554]'>
                  {extractedChildren[2]}
                </div>
              )}
              <div className='h-[45px] lg:h-[40px] w-full rounded-b-[6px]'>
                {extractedChildren[3]}
              </div>
            </div>
          </div>
        </div>
      </SmartLink>
      {/* Warning Modal */}
      {storePopUpWarning && showWarningModal && (
        <Modal
          cancelText=''
          centered
          classNames={{
            content: '!bg-container',
            header: '!bg-container !text-blackWhite',
          }}
          closeIcon={<CrossSVG className='text-blackWhite w-[16px]' />}
          destroyOnClose={true}
          footer={<></>}
          maskClosable={true}
          okText=''
          onCancel={() => setShowWarningModal(false)}
          open={showWarningModal}
          title={
            <h4 className='text-[16px] font-bold text-blackWhite'>
              Important Points on the Store
            </h4>
          }
        >
          <div
            className='p-4 text-blackWhite'
            dangerouslySetInnerHTML={{ __html: storePopUpWarning }}
          />
          {/* //Add a button to close the modal  with theme button*/}
          <ThemeButton
            className='w-36 mx-auto mt-4'
            onClick={() => handleContinueShopping()}
            text='Continue Shopping'
          />
        </Modal>
      )}
    </>
  );
};

const OfferNewBadge = ({ title }: { title?: string }) => {
  return (
    <div className='absolute top-[-8px] left-[-5px] drop-shadow-md'>
      <Image alt='offer badge' src={offerBadge} />
      <span className='text-[7px] text-white absolute top-[10px] left-[8px] rotate-[-38.297deg]'>
        {title ?? 'NEW'}
      </span>
    </div>
  );
};
export default MainOfferCard;
