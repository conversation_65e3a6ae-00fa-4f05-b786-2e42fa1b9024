'use client';
import { useState, useRef, useEffect } from 'react';
import AllLinks from '../svg/all-links';
import CashbackHandSVG from '../svg/cashback-hand';
import ICBCardSVG from '../svg/icb-card';
import { useRouter, usePathname } from 'next/navigation';
import { Coins } from 'lucide-react';
import { useForesightBatch } from '@/utils/foresight-hooks';
import ShareAndEarnSVG from '../svg/share-and-earn';

function BottomNavbar() {
  const router = useRouter();
  const pathname = usePathname();
  const [active, setActive] = useState(1);
  const { registerElements } = useForesightBatch();

  // Refs for each navigation item
  const homeRef = useRef<HTMLDivElement>(null);
  const cardRef = useRef<HTMLDivElement>(null);
  const referralRef = useRef<HTMLDivElement>(null);
  const shareEarnRef = useRef<HTMLDivElement>(null);
  const allLinksRef = useRef<HTMLDivElement>(null);

  // Register all navigation elements with ForesightJS
  useEffect(() => {
    const elements = [
      {
        element: homeRef.current,
        url: '/',
        options: { hitSlop: 30, name: 'bottom-nav-home' },
      },
      {
        element: referralRef.current,
        url: '/referral',
        options: { hitSlop: 30, name: 'bottom-nav-referral' },
      },
      {
        element: shareEarnRef.current,
        url: '/share-and-earn',
        options: { hitSlop: 30, name: 'bottom-nav-share-earn' },
      },
      {
        element: allLinksRef.current,
        url: '/all-links',
        options: { hitSlop: 30, name: 'bottom-nav-all-links' },
      },
    ];

    registerElements(elements);
  }, [registerElements]);

  // Set active state based on current pathname
  useEffect(() => {
    if (pathname === '/') {
      setActive(1);
    } else if (pathname === '/referral') {
      setActive(4);
    } else if (pathname === '/share-and-earn') {
      setActive(2);
    } else if (pathname === '/all-links') {
      setActive(5);
    }
  }, [pathname]);

  return (
    <div
      className={`bottomNavbarGradient lg:hidden fixed -bottom-[1px] w-full mx-auto bg-[#FCFCFC] z-50 flex justify-around items-center`}
    >
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(1);
          router.push('/');
        }}
        ref={homeRef}
      >
        {active === 1 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <CashbackHandSVG
          className={`w-[25px] ${
            active === 1
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        />
        <span
          className={`text-[8px] mt-[6px] ${
            active === 1
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        >
          CASHBACK
        </span>
      </div>
      {/* <div className=' text-center relative h-full flex flex-col justify-center items-center' onClick={()=>setActive(2)}>
      {active === 2 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <GiftCardSVG className='w-[25px] text-[#7b7b7b] dark:text-[#63697A]' />
        <span className='text-[#7b7b7b] text-[8px] dark:text-[#63697A] mt-[6px]'>
          GIFT CARD
        </span>
      </div> */}
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(3);
          router.push('https://card.indiancashback.com/');
        }}
        ref={cardRef}
      >
        {active === 3 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <ICBCardSVG
          className={`w-[22px] ${
            active === 3
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        />
        <span
          className={`text-[8px] mt-[4px] ${
            active === 3
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        >
          ICB CARD
        </span>
      </div>
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(2);
          router.push('/share-and-earn');
        }}
        ref={shareEarnRef}
      >
        {active === 2 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <ShareAndEarnSVG
          className={`w-[20px] ${
            active === 2
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        />
        <span
          className={`text-[8px] mt-[4px] ${
            active === 2
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        >
          SHARE & EARN
        </span>
      </div>
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(4);
          router.push('/referral');
        }}
        ref={referralRef}
      >
        {active === 4 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <Coins
          className={`w-[22px] -mt-1 ${
            active === 4
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        />
        <span
          className={`text-[8px] mt-[4px] ${
            active === 4
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        >
          REFER & EARN
        </span>
      </div>
      <div
        className='text-center relative h-full flex flex-col justify-center items-center py-4'
        onClick={() => {
          setActive(5);
          router.push('/all-links');
        }}
        ref={allLinksRef}
      >
        {active === 5 && (
          <span className='bg-[#7366D9] h-[3px] flex rounded-b w-full absolute top-0 ' />
        )}
        <AllLinks
          className={`w-[20px] ${
            active === 5
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        />
        <span
          className={`text-[8px] mt-[4px] ${
            active === 5
              ? 'text-[#7366D9]'
              : 'text-[#7b7b7b] dark:text-[#63697A]'
          }`}
        >
          ALL LINKS
        </span>
      </div>
    </div>
  );
}

export default BottomNavbar;
