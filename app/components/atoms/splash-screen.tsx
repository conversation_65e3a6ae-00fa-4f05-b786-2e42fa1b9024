'use client';
import React, { useEffect, useState } from 'react';
import PageLoader from '../misc/loading-components';

const SplashScreen = () => {
  const [showSplash, setShowSplash] = useState(false);
  const [isHydrated, setIsHydrated] = useState(false);

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  useEffect(() => {
    if (!isHydrated) {
      return;
    }

    const rawData = localStorage.getItem('splashScreenShown');
    if (!rawData) {
      localStorage.setItem('splashScreenShown', JSON.stringify(true));
      setShowSplash(true);
      const delay = async () => {
        // simulating a delay
        await new Promise((resolve) => setTimeout(() => resolve(''), 3000));
        // After the delay, hide the splash screen
        setShowSplash(false);
      };
      delay();
    } else {
      setShowSplash(false);
    }
  }, [isHydrated]);

  return showSplash && <PageLoader isMainLoader={true} />;
};

export default SplashScreen;
