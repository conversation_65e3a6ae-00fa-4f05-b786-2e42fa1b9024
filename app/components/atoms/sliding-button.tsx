'use client';

import clsx from 'clsx';
import React, { useEffect, useState } from 'react';

const SlidingButton = ({
  buttonDetails,
  uniqueId,
  onChange,
  defaultSelectedBtn,
  rootClassName,
}: {
  buttonDetails: Array<{
    title: string;
    value: string;
    mobileTitle?: string; // Optional mobile-friendly title
  }>;
  uniqueId: string;
  onChange: (e: any) => void;
  defaultSelectedBtn: number;
  rootClassName?: string;
}) => {
  const [isMobile, setIsMobile] = useState(false);

  useEffect(() => {
    const checkMobile = () => {
      setIsMobile(window.innerWidth < 640);
    };

    checkMobile();
    window.addEventListener('resize', checkMobile);
    return () => window.removeEventListener('resize', checkMobile);
  }, []);

  useEffect(() => {
    const ele = document.getElementById(
      `radio${defaultSelectedBtn}-${uniqueId}`
    );
    if (ele && ele instanceof HTMLInputElement) {
      ele.checked = true;
    }
  }, [defaultSelectedBtn, uniqueId]);

  return (
    <div className={clsx(rootClassName, 'slidingBtnContainer')}>
      <div className='slidingBtnTabs dark:bg-[#3E424C]'>
        {buttonDetails.map((item, index) => (
          <React.Fragment key={index}>
            <input
              id={`radio${index + 1}-${uniqueId}`}
              name={uniqueId}
              onChange={onChange}
              type='radio'
              value={item.value}
            />
            <label
              className='slidingBtnTab'
              htmlFor={`radio${index + 1}-${uniqueId}`}
              title={item.title} // Show full title on hover
            >
              {isMobile && item.mobileTitle ? item.mobileTitle : item.title}
            </label>
          </React.Fragment>
        ))}
        <span className='glider' />
      </div>
    </div>
  );
};

export default SlidingButton;
