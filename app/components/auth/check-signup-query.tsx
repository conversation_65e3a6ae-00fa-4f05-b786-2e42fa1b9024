'use client';

import { useAppDispatch } from '@/redux/hooks';
import {
  setLoginModalOpen,
  setLoginSignupScreen,
} from '@/redux/slices/auth-slice';
import { useSearchParams } from 'next/navigation';
import { useEffect } from 'react';

const CheckSignupQuery = () => {
  const searchParams = useSearchParams();
  const dispatch = useAppDispatch();

  useEffect(() => {
    const signupParam = searchParams.get('signup');
    const referralCode = searchParams.get('r');

    if (
      signupParam === 'true' ||
      (referralCode?.length && referralCode.length > 0)
    ) {
      if (referralCode?.length && referralCode.length > 0) {
        localStorage.setItem('referralCode', `${referralCode}`);
      }
      // Set the screen to signup and open the modal
      dispatch(setLoginSignupScreen({ for: 'signup', step: 1 }));
      dispatch(setLoginModalOpen(true));
    }
  }, [searchParams, dispatch]);

  // This is a utility component that doesn't render anything
  return null;
};

export default CheckSignupQuery;
