'use client';
import React, { useMemo, useRef, useState } from 'react';
import CommonContainer from '../../common-container';
import Pattern2 from '../../svg/patterns/pattern2';
import Image from 'next/image';
import findByCategoryImg from '@/public/img/find-by-category.png';
import FindByCategoryCard from '../../cards/find-by-category-card';
import Pattern5 from '../../svg/patterns/pattern5';
import SectionSeeAllBtn from '../../atoms/section-see-all';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';
import { sideScroll } from '@/utils/helpers';
import Tab from '../../my-tabs/tab';
import Tabs from '../../my-tabs/tabs';
import type { CategoryStoresResponse } from '@/services/api/data-contracts';
import type { PromiseStatus } from '@/types/global-types';
import { useRouter } from 'next/navigation';
import ThemeButton from '../../atoms/theme-btn';
import { ChevronRightCircle } from 'lucide-react';
import { useWindowSize } from 'usehooks-ts';

const Index = ({
  promiseStatus,
  storesByCat,
}: {
  promiseStatus: PromiseStatus;
  storesByCat: CategoryStoresResponse[];
}) => {
  const [value, setValue] = useState(0);
  const [isTabLoading, setIsTabLoading] = useState(false);
  const containerRef = useRef<HTMLDivElement | null>(null);
  const router = useRouter();
  const isLoading = promiseStatus !== 'fulfilled';
  const { width = 0 } = useWindowSize();

  // if mobile then limit stores upto 6 only
  const filteredStores = useMemo(() => {
    if (storesByCat.length > 0 && storesByCat[value]?.stores && width < 640) {
      return storesByCat[value].stores.slice(0, 6);
    }

    return storesByCat[value].stores || [];
  }, [storesByCat, value, width]);

  // Create skeleton loaders when data is loading
  const renderSkeletonLoaders = () => {
    return Array(6)
      .fill(0)
      .map((item) => (
        <FindByCategoryCard
          bgColor=''
          deals={0}
          isLoading={true}
          key={`skeleton-loader-${item}`}
          offering=''
          title=''
          url=''
        />
      ));
  };

  // Create skeleton tabs when data is loading
  const renderSkeletonTabs = () => {
    return (
      <div className='flex gap-x-[10px] lg:gap-x-5 pb-2'>
        {Array(5)
          .fill(0)
          .map((item) => (
            <div
              className='h-[24px] min-w-[80px] bg-gray-200 dark:bg-gray-700 rounded animate-pulse'
              key={`skeleton-tab-${item}`}
            />
          ))}
      </div>
    );
  };

  return (
    <CommonContainer className='findbyCategorySection lg:flex lg:rounded-none'>
      <div className='relative bg-transparent lg:bg-[#EAEAEA] lg:dark:bg-[#1f2022] lg:w-[162px] flex justify-center shrink-0'>
        <Pattern2 className='text-[#E2E2E2] dark:text-[#3B3D45] shrink-0 absolute top-[15px] left-[13px]' />
        <Pattern5 className='text-[#D7D7D7] dark:text-[#3B3D45] shrink-0 absolute hidden lg:block top-[15px] left-[13px]' />
        <div className='flex mt-[21px] lg:mt-0 items-center justify-center lg:flex-col lg:w-min text-center'>
          <Image
            alt='shop image'
            className='w-[40px] h-[40px] lg:w-[80px] lg:h-[80px]'
            quality={100}
            src={findByCategoryImg}
          />
          <h3 className='text-sm md:text-lg lg:text-sm lg:font-[400] text-heading font-medium font-pat ml-[11px] lg:ml-0'>
            Top Stores
          </h3>
          <SectionSeeAllBtn
            isMobile={false}
            onClick={() => router.push('online-free-shopping-stores')}
          />
        </div>
      </div>

      <LeftRoundButton
        classCont='mt-[0px] ml-[12px]'
        onClick={() => sideScroll(containerRef.current, 10, 400, -10)}
      />

      <div className='overflow-hidden grow customScrollbar'>
        {/* -------------------Tabs with fixed height container to prevent layout shift---------------------- */}
        <div className='h-[60px] lg:h-[75px] w-full'>
          <div className='select_category flex mt-[20px] overflow-x-auto border-b-[2px] border-primary text-[13px] text-content font-[300] pl-4 pr-4 md:px-0 md:justify-center min-h-[40px]'>
            {isLoading ? (
              renderSkeletonTabs()
            ) : (
              <Tabs
                className='gap-x-[10px] lg:gap-x-5'
                onChange={(id: number) => {
                  setIsTabLoading(true);
                  setValue(id);

                  // Simulate loading delay for better UX
                  setTimeout(() => {
                    setIsTabLoading(false);
                  }, 300);

                  // Get the clicked tab element and scroll it into view
                  const tabElements = document.querySelectorAll(
                    '.select_category span[role="button"]'
                  );
                  if (tabElements[id]) {
                    tabElements[id].scrollIntoView({
                      behavior: 'smooth',
                      block: 'nearest',
                      inline: 'center',
                    });
                  }
                }}
                value={value}
              >
                {storesByCat.map((item) => (
                  <Tab key={item.categoryName}>{item.categoryName}</Tab>
                ))}
              </Tabs>
            )}
          </div>
        </div>

        <div
          className='grid grid-cols-2 min-[500px]:grid-cols-3 min-[700px]:grid-cols-4 gap-4 mt-[10px] px-[8px] lg:gap-6 lg:flex lg:pt-8 lg:mx-[30px] overflow-auto w-full lg:w-[calc(100%-60px)] customScrollbar lg:pb-[25px] min-h-[180px]'
          key={`stores-container-${value}-${isTabLoading}`}
          ref={containerRef}
        >
          {(() => {
            if (isLoading || isTabLoading) {
              return renderSkeletonLoaders();
            }
            return (
              <>
                {filteredStores.map((item) => (
                  <FindByCategoryCard
                    bgColor={item.bgColor}
                    className='w-auto md:w-full lg:w-[180px]'
                    deals={item.offerCount}
                    isLoading={false}
                    key={item.uid}
                    offering={item.caption}
                    title={item.storeName}
                    url={item.imageUrl}
                  />
                )) || []}
                {/* See All Card */}
                <ThemeButton
                  className='!w-fit hidden lg:flex text-xs sm:text-sm lg:text-base mx-auto uppercase mr-4 mt-[30px] whitespace-nowrap px-3'
                  icon={<ChevronRightCircle className='size-6 ml-2' />}
                  isDisabled={isLoading || isTabLoading}
                  onClick={() => router.push('online-free-shopping-stores')}
                  text='See All Stores'
                />
              </>
            );
          })()}
        </div>
      </div>
      <RightRoundButton
        classCont='mt-[0px] ml-[6px]'
        onClick={() => sideScroll(containerRef.current, 10, 400, 10)}
      />

      <SectionSeeAllBtn
        onClick={() => router.push('online-free-shopping-stores')}
        text='See All Stores'
      />
    </CommonContainer>
  );
};

export default Index;
