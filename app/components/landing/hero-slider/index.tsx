'use client';
import React, { useRef } from 'react';
import CoverflowSwiper from './coverflow-swiper';
import { Swiper, SwiperSlide } from 'swiper/react';
import type { Swiper as SwiperType } from 'swiper';
import 'swiper/css';
import 'swiper/css/pagination';
import 'swiper/css/navigation';
import { Autoplay, Pagination, EffectFade, Navigation } from 'swiper/modules';
import Image from 'next/image';
import type { MyBannerResponse, PromiseStatus } from '@/types/global-types';
import {
  ensureFourSlides,
  ensureFourSlidesDesktop,
} from '../../../../utils/helpers';
import ShimmerEffect from '../../atoms/shimmer-effect';
import { motion } from 'framer-motion';
import 'swiper/css/effect-fade';
import SmartLink from '../../common/smart-link';
import { LinkType } from '@/utils/link-utils';
import { LeftRoundButton, RightRoundButton } from '../../atoms/rounded-buttons';

const Index = ({
  heroSliderData,
  promiseStatus,
}: {
  heroSliderData: MyBannerResponse;
  promiseStatus: PromiseStatus;
}) => {
  const swiperRef = useRef<SwiperType | null>(null);

  const containerVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.6,
        ease: 'easeOut',
      },
    },
  };

  if (promiseStatus !== 'fulfilled') {
    return (
      <motion.div
        animate='visible'
        className='mt-[18px] lg:mt-[8px]'
        initial='hidden'
        variants={containerVariants}
      >
        {/* Mobile Skeleton Loader */}
        <div className='lg:hidden'>
          <div className='coverflowSwiperWrapper'>
            <div className='relative w-full h-[180px] sm:h-[220px] overflow-hidden rounded-[12px]'>
              <ShimmerEffect className='w-full h-full' />
            </div>
          </div>
        </div>

        {/* Desktop Skeleton Loader */}
        <div className='hidden lg:block'>
          <div className='relative aspect-[27/5] w-full mx-auto overflow-hidden rounded-[12px]'>
            <ShimmerEffect className='w-full h-full' />
          </div>
        </div>
      </motion.div>
    );
  }

  if (
    promiseStatus !== 'fulfilled' ||
    (!heroSliderData?.desktopBanners?.length &&
      !heroSliderData?.mobileBanners?.length)
  ) {
    return null;
  }

  const { desktopBanners, mobileBanners } = heroSliderData;

  return (
    <motion.div
      animate='visible'
      className='mt-8 lg:mt-2 px-2'
      initial='hidden'
      variants={containerVariants}
    >
      {/* Mobile Swiper with CoverFlow effect */}
      <CoverflowSwiper className='lg:hidden' swiperName='heroCoverSwiper'>
        {ensureFourSlides(mobileBanners).map((item) => (
          <SwiperSlide key={item.imageUrl}>
            <SmartLink
              href={item.redirectUrl || ''}
              linkType={LinkType.INTERNAL}
            >
              <div className='relative w-full h-full'>
                <Image
                  alt='slider img'
                  className='hero_swiperImg object-cover'
                  fill
                  priority
                  quality={100}
                  sizes='(max-width: 1023px) 100vw, 0vw'
                  src={item.imageUrl || '/temp/heroSlider1.png'}
                />
              </div>
            </SmartLink>
          </SwiperSlide>
        ))}
      </CoverflowSwiper>

      {/* Desktop Swiper */}
      <div className='relative hidden lg:block'>
        <Swiper
          autoplay={{
            delay: 4000,
            disableOnInteraction: false,
            pauseOnMouseEnter: true,
          }}
          centeredSlides={true}
          className={'desktopHeroSwiper'}
          effect={'fade'}
          grabCursor={true}
          loop={true}
          modules={[Pagination, Autoplay, EffectFade, Navigation]}
          onSwiper={(swiper) => {
            swiperRef.current = swiper;
          }}
          pagination={{
            clickable: true,
            enabled: false,
            bulletActiveClass: '!bg-white !opacity-[1]',
            bulletClass: 'swiper-pagination-bullet !bg-white/50 !w-2 !h-2',
          }}
          slidesPerView={1}
          speed={800}
        >
          {ensureFourSlidesDesktop(desktopBanners).map((item) => (
            <SwiperSlide key={item.imageUrl}>
              <motion.div
                transition={{ duration: 0.3 }}
                whileHover={{ scale: 1.001 }}
              >
                <SmartLink
                  href={item.redirectUrl || ''}
                  linkType={LinkType.INTERNAL}
                >
                  <div className='relative aspect-[27/5] w-full mx-auto overflow-hidden rounded-[12px] shadow-xl'>
                    <Image
                      alt='slider img'
                      className='w-full h-full object-cover transition-transform duration-500 hover:scale-[1.01]'
                      fill
                      priority
                      quality={100}
                      sizes='(max-width: 768px) 0vw, 100vw'
                      src={item.imageUrl || '/temp/desktopHeroSlides/1.webp'}
                    />
                  </div>
                </SmartLink>
              </motion.div>
            </SwiperSlide>
          ))}
        </Swiper>

        {/* Navigation Buttons */}
        <LeftRoundButton
          classCont='!left-4 !top-1/2 !-translate-y-1/2 hover:scale-105'
          onClick={() => swiperRef.current?.slidePrev()}
        />
        <RightRoundButton
          classCont='!right-4 !top-1/2 !-translate-y-1/2 hover:scale-105'
          onClick={() => swiperRef.current?.slideNext()}
        />
      </div>
    </motion.div>
  );
};

export default Index;
