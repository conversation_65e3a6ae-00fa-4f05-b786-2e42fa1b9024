'use client';
import Right<PERSON>rrow from '../svg/right-arrow';
import WalletAddSVG from '../svg/wallet-add';
import CancelledSVG from '../svg/cancelled';
import PendingSVG from '../svg/pending';
import { UserOverviewResponse } from '@/services/api/data-contracts';
import fetchWrapper from '@/utils/fetch-wrapper';
import { memo, useState, useEffect } from 'react';
import { toast } from 'react-toastify';
import { useAppDispatch } from '@/redux/hooks';
import { isAuthError } from '@/utils/auth-utils';
import Link from 'next/link';

const getEarningsOverview = async () => {
  return await fetchWrapper<UserOverviewResponse>(`/api/proxy/users/overview`);
};

const CashbackSummary = () => {
  const [data, setData] = useState<UserOverviewResponse | undefined>(undefined);
  const dispatch = useAppDispatch();

  useEffect(() => {
    const fetchData = async () => {
      try {
        const result = await getEarningsOverview();
        setData(result);
      } catch (err) {
        // Check if it's an authentication error
        if (isAuthError(err)) {
          // Auth error is already handled globally by fetchWrapper
          // Just log it and don't show additional error toast
          console.log('Auth error in CashbackSummary - handled globally');
          return;
        }

        // For non-auth errors, show the error toast
        toast.error('Failed to fetch user summary');
        console.log(err);
      }
    };
    fetchData();
  }, [dispatch]);
  return (
    <Link
      className='w-[85%] md:w-[55%] min-w-[295px] focus:scale-105 h-[90px] rounded-[10px] absolute top-[70px] left-[50%] -translate-x-[50%] bg-white flex flex-col'
      href='/my-profile'
    >
      <div className='h-[41px] rounded-inherit bg-container text-cashbackEarned rounded-t-[10px] pt-[17px] px-[17px] flex items-start justify-between'>
        <h4 className='text-[#5E50CC] dark:text-white text-[10px] font-medium tracking-[1.19px] leading-normal'>
          Lifetime Cashback Earned
        </h4>
        <h2 className='text-[#5E50CC] dark:text-white text-[12px] font-black -tracking-[0.48px] leading-normal font-nexa'>
          ₹ {data?.totalCashbackEarned.toLocaleString()}
        </h2>
      </div>
      <div className='rounded-b-[10px] text-white relative h-[49px] w-full flex items-center justify-evenly bg-[#8374FC]'>
        <div className='bg-[#FFC554] w-[28px] h-[4px] -rotate-90 absolute -left-[12px] top-[22px] rounded-b-[10px]' />

        <div className='readyToWithdraw flex items-start'>
          <WalletAddSVG className='text-white w-[15px]' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              ₹ {data?.readyToWithdraw.toLocaleString()}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              Ready to withdraw
            </span>
          </div>
        </div>

        <div className='dash w-[1px] h-[25px] bg-[#E8E5FF] shrink-0' />

        <div className='pending flex items-start'>
          <PendingSVG className='w-[15px] text-white' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              {data?.totalPendingCount}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              Pending
            </span>
          </div>
        </div>

        <div className='dash w-[1px] h-[25px] bg-[#E8E5FF] shrink-0' />

        <div className='cancelled flex items-start'>
          <CancelledSVG className='w-[15px] text-white' />
          <div className='flex items-between flex-col ms-[10px]'>
            <h2 className='text-sm font-black -tracking-[0.56px] leading-none font-nexa'>
              {data?.totalApprovedCount}
            </h2>
            <span className='text-[7px] sm:text-[9px] leading-normal'>
              Confirmed
            </span>
          </div>
        </div>
        <RightArrow className='text-white w-[12px]' />
      </div>
    </Link>
  );
};

export default memo(CashbackSummary);
