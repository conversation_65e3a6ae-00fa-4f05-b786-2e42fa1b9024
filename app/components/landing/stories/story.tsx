'use client';
import {
  setCurrentStoryIndex,
  setIsStoryModalOpen,
  setStoriesSeen,
} from '@/redux/slices/stories-slice';
import { RootState } from '@/redux/store';
import { timeAgo } from '@/utils/helpers';
import dynamic from 'next/dynamic';
import Image from 'next/image';
const StoriesLazy = dynamic(() => import('react-insta-stories'), {
  ssr: false,
});

import React, { useCallback, useState, useEffect } from 'react';
import { useDispatch, useSelector } from 'react-redux';
import CrossSVG from '../../svg/cross';
import { MobileStoryResponse } from '@/services/api/data-contracts';
import SmartLink from '../../common/smart-link';
import { LinkType } from '@/utils/link-utils';

// Type for stories seen tracking
type StoriesSeen = Record<string, number>;

const Story = ({
  stories,
  storeLogo,
  storeName,
  slidesLength,
  handleChangeSlide,
}: {
  stories: MobileStoryResponse[];
  storeLogo: string;
  storeName: string;
  slidesLength: number;
  handleChangeSlide: () => void;
}) => {
  const storiesLen = stories.length;
  const { currentSlideIndex, currentStoryIndex } = useSelector(
    (state: RootState) => state.stories
  );
  const [isHydrated, setIsHydrated] = useState(false);
  const dispatch = useDispatch();

  useEffect(() => {
    setIsHydrated(true);
  }, []);

  const handleStoryEnd = (storyEndIndex: number) => {
    if (currentSlideIndex !== slidesLength - 1) {
      storyEndIndex === storiesLen - 1 && handleChangeSlide();
    } else {
      storyEndIndex === storiesLen - 1 && dispatch(setIsStoryModalOpen(false));
    }
  };

  const handleStoryStart = (storyStartIndex: number) => {
    dispatch(setCurrentStoryIndex(storyStartIndex));

    // Only access localStorage after hydration
    if (!isHydrated) {
      return;
    }

    const storedData = localStorage.getItem('storiesSeen');
    if (storedData) {
      const storiesSeen = JSON.parse(storedData);
      if (
        storiesSeen?.[storeName] <= storyStartIndex + 1 ||
        storiesSeen?.[storeName] === undefined
      ) {
        const newStoriesSeen: StoriesSeen = {
          ...storiesSeen,
          [storeName]: storyStartIndex + 1,
        };
        dispatch(setStoriesSeen(newStoriesSeen));
        const storiesSeenStringfy = JSON.stringify(newStoriesSeen);
        localStorage.setItem('storiesSeen', storiesSeenStringfy);
      }
    } else {
      dispatch(setStoriesSeen({ [storeName]: 1 }));
      const storiesSeenStringfy = JSON.stringify({ [storeName]: 1 });
      localStorage.setItem('storiesSeen', storiesSeenStringfy);
    }
  };

  const getOptimizedStoryObject = useCallback(() => {
    function getStoriesObject() {
      const storiesData = stories.map((item) => {
        return {
          //eslint-disable-next-line
          content: (props: any) => (
            <div className='story-container bg-black w-screen flex items-start justify-center'>
              <div className='relative w-full h-[100svh] bg-black max-w-screen-md flex'>
                <Image
                  alt='story img'
                  className='object-contain'
                  fill
                  loading='eager'
                  sizes='(max-width:1023px) 100vw, 0vw'
                  src={item.imageUrl || ''}
                />
                <div className='storyHeader flex gap-[10px] h-[50px] absolute top-[25px] left-[14px] z-[9999]'>
                  {/* top-[50%] -translate-y-[50%] mx-auto */}
                  <div className='relative w-[50px] h-[50px] bg-[#8374FC] rounded-full overflow-hidden drop-shadow-lg'>
                    <Image
                      alt='store logo'
                      className='object-contain'
                      layout='fill'
                      src={storeLogo || ''}
                    />
                  </div>
                  <div className='flex flex-col justify-center drop-shadow-lg'>
                    <span
                      className='text-white font-bold text-sm'
                      style={{ textShadow: '1px 1px 4px black' }}
                    >
                      {storeName}
                    </span>
                    <span
                      className='text-white text-xs'
                      style={{ textShadow: '1px 1px 4px black' }}
                    >
                      {timeAgo(item.timestamp || Date.now())}
                    </span>
                  </div>
                  <button
                    className='close-story drop-shadow-md w-[30px] h-[30px] bg-[#00000038] rounded-md flex-center'
                    onClick={() => dispatch(setIsStoryModalOpen(false))}
                  >
                    <CrossSVG className='w-[15px] h-[15px] text-white' />
                  </button>
                </div>

                <div className='storyFooter w-full absolute h-[280px] bottom-0 z-[99] text-white px-[16px] pb-[25px] storyGradient flex flex-col justify-end'>
                  {item?.title ? (
                    <h5 className='text-[10px] font-medium ml-[12px]'>
                      {item.title}
                    </h5>
                  ) : null}
                  {item?.description ? (
                    <div className='mt-[7px] leading-none ml-[10px]'>
                      <Image
                        alt='cashaback'
                        className='inline max-w-[16px] max-h-[16px]'
                        height={16}
                        src='/svg/cashback.svg'
                        width={16}
                      />
                      <span className='text-[12px] font-bold ml-[5px]'>
                        {item?.description}
                      </span>
                    </div>
                  ) : null}
                  <SmartLink
                    className='h-[32px] flex-center bg-primary text-white rounded-[6px] mt-[10px] shrink-0 text-[12px] font-semibold'
                    href={item?.redirectUrl || ''}
                    linkType={LinkType.INTERNAL}
                    target='_blank'
                  >
                    {item?.buttonText}
                  </SmartLink>
                </div>
              </div>
            </div>
          ),
          duration: item.duration || 4000, //in milliseconds
        };
      });
      return storiesData;
    }
    return getStoriesObject();
  }, [dispatch, storeLogo, storeName, stories]);

  return (
    <div className='story'>
      <StoriesLazy
        defaultInterval={100000}
        height='100svh'
        keyboardNavigation
        onNext={() => handleStoryEnd(currentStoryIndex)}
        onStoryEnd={(storyEndIndex: number) => handleStoryEnd(storyEndIndex)}
        onStoryStart={(s: number) => handleStoryStart(s)}
        progressStyles={{ background: '#8374FC' }}
        progressWrapperStyles={{
          background: 'white',
          height: '3px',
        }}
        stories={getOptimizedStoryObject()}
        width='100%'
        // onPrevious={}
      />
    </div>
  );
};

export default Story;
