import type { CustomSearchParamsTypes } from '@/types/global-types';
import { BASE_URL } from '@/config';
import IndexStoreClients from './index-clients';
import type {
  CashbackRateType,
  DealAndCouponsResponse,
  GetAllReviewsResponse,
  GetAllStoresResponse,
  GetCashbackRatesByStoreResponse,
  GetStoreDetailsResponse,
} from '@/services/api/data-contracts';
import { cookies } from 'next/headers';
import fetchWrapper from '@/utils/fetch-wrapper';
import type { Metadata } from 'next';
import { generateStoreSchema } from '@/utils/schema';

// Generate static params for popular stores
export async function generateStaticParams() {
  try {
    // Fetch popular stores for static generation
    const storesData = await fetchWrapper<GetAllStoresResponse>(
      `${BASE_URL}/stores?searchParam=&sortType=popular&minPercent=0&maxPercent=100&page=1&pageSize=50&saved=false`,
      {
        // Use a longer cache for build-time generation
        next: { revalidate: 3600 }, // 1 hour
      }
    );

    // Generate params for the most popular stores
    if (!Array.isArray(storesData?.stores)) {
      console.warn('stores data is not an array:', storesData?.stores);
      return [];
    }

    return storesData.stores.slice(0, 30).map((store) => ({
      slug: encodeURIComponent(store?.storeName || ''),
    }));
  } catch (error) {
    console.error('Error generating static params for stores:', error);
    // Return empty array if API fails during build
    return [];
  }
}

export async function generateMetadata({
  params,
}: {
  params: { slug: string };
}): Promise<Metadata> {
  try {
    const storeName =
      typeof params.slug === 'string'
        ? decodeURIComponent(params.slug)
        : params.slug;

    const resData = await getStoreData(
      {} as CustomSearchParamsTypes,
      storeName,
      undefined // No token needed for metadata
    );
    const currentYear = new Date().getFullYear();

    return {
      title: `${resData?.store?.name} - Offers, Deals & coupon codes, ${currentYear}`,
      description: `'Visit ${resData?.store?.name} and get rewarded for every purchase you make - ${resData?.store?.name} coupons, promo codes and discount offers.`,
      alternates: {
        canonical: `https://www.indiancashback.com/store/${encodeURIComponent(
          storeName
        )}`,
      },
      openGraph: {
        url: `https://www.indiancashback.com/store/${resData?.store?.name}`,
      },
    };
  } catch (err) {
    console.error(err);
    return {
      title: 'Store Not Found',
      description: 'The requested store could not be found.',
    };
  }
}

async function getStoreData(
  searchParams: CustomSearchParamsTypes,
  storeName: string,
  token?: string
) {
  const {
    searchParam = '',
    sortType = 'newest',
    subCategories = '',
    userType = 'both',
    offerType = 'deals',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const res = await fetchWrapper<GetStoreDetailsResponse>(
    `${BASE_URL}/stores/store-details${storeName}?searchParam=${searchParam}&sortType=${sortType}&userType=${userType}&offerType=${offerType}&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token,
      suppressToast: true,
      next: { revalidate: 240 }, // Add ISR caching
    }
  );
  return res;
}

async function getStoreOffers(
  searchParams: CustomSearchParamsTypes,
  storeId: string,
  token?: string
) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const res = await fetchWrapper<DealAndCouponsResponse>(
    `${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&storeId=${storeId}&userType=${userType}&offerType=deals&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token,
      next: { revalidate: 240 }, // Add ISR caching
    }
  );
  return res;
}

async function getStoreCoupons(
  searchParams: CustomSearchParamsTypes,
  storeId: string,
  token?: string
) {
  const {
    searchParam = '',
    sortType = 'popular',
    subCategories = '',
    userType = 'both',
    page = '1',
    pageSize = '15',
  } = searchParams;

  const res = await fetchWrapper<DealAndCouponsResponse>(
    `${BASE_URL}/offers/deals-and-coupons?searchParam=${searchParam}&sortType=${sortType}&storeId=${storeId}&userType=${userType}&offerType=coupons&subCategories=${subCategories}&page=${page}&pageSize=${pageSize}`,
    {
      token,
      next: { revalidate: 240 }, // Add ISR caching
    }
  );
  return res;
}

async function getStoreReviews(
  searchParams: CustomSearchParamsTypes,
  storeId: string,
  token?: string
) {
  const { reviewSortType, page = '1', pageSize = '15' } = searchParams;
  let queryParams = `storeId=${storeId}&page=${page}&pageSize=${pageSize}`;

  if (reviewSortType) {
    queryParams += `&sortType=${reviewSortType}`;
  }

  const res = await fetchWrapper<GetAllReviewsResponse>(
    `${BASE_URL}/stores/review?${queryParams}`,
    {
      token,
      next: { revalidate: 240 }, // Add ISR caching
    }
  );
  return res;
}

async function getCbRatesData(storeId: string) {
  const res = await fetchWrapper<GetCashbackRatesByStoreResponse>(
    `${BASE_URL}/stores/cashback-rates-by-store${storeId}`,
    {
      next: { revalidate: 240 }, // Add ISR caching
    }
  );
  return res;
}

const Page = async ({
  params,
  searchParams,
}: {
  params: { slug: string };
  searchParams: CustomSearchParamsTypes;
}) => {
  const storeName =
    typeof params.slug === 'string'
      ? decodeURIComponent(params.slug)
      : params.slug;

  // Get token from cookies in the main page component
  const cookieStore = cookies();
  const token = cookieStore.get('accessToken')?.value;

  let resData: GetStoreDetailsResponse;
  let storeOffers: DealAndCouponsResponse;
  let storeCoupons: DealAndCouponsResponse;
  let storeReviews: GetAllReviewsResponse;
  let cbRatesData: CashbackRateType[] = [];

  try {
    resData = await getStoreData(searchParams, storeName, token);
    storeOffers = await getStoreOffers(searchParams, resData.store.id, token);
    storeCoupons = await getStoreCoupons(searchParams, resData.store.id, token);
    storeReviews = await getStoreReviews(searchParams, resData.store.id, token);
    const resCbRates = await getCbRatesData(resData.store.id);
    cbRatesData = resCbRates.cashbackRates;
  } catch (err: any) {
    console.log({ err });
    return (
      <div className='error-container'>
        <h1>Error loading store</h1>
        <p>Please try again later.</p>
      </div>
    );
  }

  // Generate Store Schema.org structured data
  const storeSchema = generateStoreSchema({
    name: resData.store.name,
    description:
      resData.store.description ||
      `Get the best cashback deals and offers from ${resData.store.name}`,
    image: resData.store.logo,
    url: `https://www.indiancashback.com/store/${encodeURIComponent(
      storeName
    )}`,
    offers:
      storeOffers?.offers?.slice(0, 3).map((offer) => ({
        '@type': 'Offer',
        name: offer.offerTitle,
        description:
          offer.offerCaption ||
          `${offer.offerTitle} - Get the best deals and cashbacks at ${resData.store.name}`,
        url: `https://www.indiancashback.com/offer/${encodeURIComponent(
          offer.offerTitle
        )}`,
      })) || [],
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: storeSchema }}
        type='application/ld+json'
      />
      <IndexStoreClients
        cbRatesData={cbRatesData}
        data={resData}
        storeCoupons={storeCoupons}
        storeOffers={storeOffers}
        storeReviews={storeReviews}
      />
    </>
  );
};

export default Page;

// ISR Configuration
// When using searchParams and cookies, we need to opt into dynamic rendering
// but still allow ISR for the cacheable parts
export const dynamic = 'force-dynamic';

// Allow ISR for stores not in generateStaticParams
// This enables new stores to be statically generated on-demand
export const dynamicParams = true;
