'use client';
import BreadcrumbSaveShare from '@/app/components/atoms/breadcrumb-container';
import CommonHeader from '@/app/components/headers/common-header';
import React, { useEffect, useState } from 'react';
import { motion, AnimatePresence } from 'framer-motion';
import AsidebarStore from './aside-store';
import StoreBanner from '@/app/components/cards/store-banner';
import TabsContainer from '@/app/components/atoms/tabs-container';
import MainOfferCard from '@/app/components/cards/main-offer-card';
import PlusIcon from '@/app/components/svg/plus-icon';
import CopySVG from '@/app/components/svg/copy';
import CommonToolbar, {
  dealsAndCouponsTypeSortItems,
  reviewSortItems,
} from '@/app/components/common-toolbar';
import ToolbarDropdown from '@/app/components/atoms/toolbar-dropdown';
import SortSVG from '@/app/components/svg/sort';
import RatingStars from '@/app/components/atoms/rating-stars';
import CustomerReviewInputCont from '@/app/components/misc/customer-review-input';
import BottomDrawer from '@/app/components/atoms/BottomDrawer';
import CustomerReview from '@/app/components/cards/customer-review';
import Image from 'next/image';
import SearchInput from '@/app/components/atoms/search-input';
import CbRatesAccordian from '@/app/components/accordians/cb-rates-accordian';
import ImportantPointCard from '@/app/components/cards/important-point-cb-rates';
import ImportantUpdateBadge from '@/app/components/svg/important-update-badge';
import CommonFilterMobile from '@/app/components/misc/common-filter-mobile';
import { ConfigProvider, Pagination, theme } from 'antd';
import type {
  CashbackRateType,
  CreateReviewDto,
  CreateReviewDtoRatingEnum,
  DealAndCouponsResponse,
  GetAllReviewsResponse,
  GetAllReviewsType,
  GetStoreDetailsResponse,
  ReviewTypes,
} from '@/services/api/data-contracts';
import {
  copyToClipboard,
  filterCashbackRates,
  formatIndRs,
} from '@/utils/helpers';
import { useAppDispatch, useAppSelector } from '@/redux/hooks';
import { useTheme } from 'next-themes';
import { usePathname, useRouter, useSearchParams } from 'next/navigation';
import {
  useCopyCoupon,
  useCreateMultiQueryString,
  useResponsiveGrid,
} from '@/utils/custom-hooks';
import { setReviewSortFilter } from '@/redux/slices/common-filters-slice';
import type { CustomSearchParamsTypes } from '@/types/global-types';
import StoreByCBCard from '@/app/components/landing/stores-cb-percentage/store-by-cb-card';
import fetchWrapper from '@/utils/fetch-wrapper';
// import SlidingButton from '@/app/components/atoms/sliding-button';
// import NoData from '@/app/components/no-data';
import EnhancedNoData from '@/app/components/enhanced-no-data';
import { setLoginModalOpen } from '@/redux/slices/auth-slice';
import { toast } from 'react-toastify';
import ShareEarnBanner from '@/app/components/landing/share-earn-banner';
const IndexStoreClients = ({
  data,
  storeOffers,
  storeCoupons,
  storeReviews,
  cbRatesData,
}: {
  data: GetStoreDetailsResponse;
  storeOffers: DealAndCouponsResponse;
  storeCoupons: DealAndCouponsResponse;
  storeReviews: GetAllReviewsResponse;
  cbRatesData: CashbackRateType[];
  searchParamList?: CustomSearchParamsTypes;
}) => {
  const { isUserLogin } = useAppSelector((state) => state.auth);

  const { userDetails } = useAppSelector((state) => state.auth);

  const [searchValue, setSearchValue] = useState('');
  const [addReviewShown, toggleAddReview] = useState(false);
  const [activeTabId, setActiveTabId] = useState(1);
  const [openReviewModal, setOpenReviewModal] = useState(false);
  const [activeId, setActiveId] = useState(1);
  const [isShowFilterModal, setShowFilterModal] = useState(false);
  const [userReviews, setUserReviews] = useState<GetAllReviewsType[]>([]);
  const [filteredCbRates, setFilteredCbRates] =
    useState<CashbackRateType[]>(cbRatesData);

  // Use the custom hook for offers/coupons section
  const { getGridProps: getOffersGridProps } = useResponsiveGrid({
    dependencies: [activeTabId],
  });

  const dispatch = useAppDispatch();
  const { resolvedTheme } = useTheme();
  const { replace } = useRouter();
  const searchParams = useSearchParams();
  const pathname = usePathname();
  const createMultiQueryString = useCreateMultiQueryString(searchParams);
  const importantPoints = [
    {
      url: '/svg/important-points/illustration1.svg',
      imgCaption: `₹ ${data?.store?.minimumAmount}`,
      title: 'Minimum Transaction Amount',
    },
    {
      url: '/svg/important-points/illustration2.svg',
      imgCaption: data?.store?.trackingTime,
      title: 'Tracking Time',
    },
    {
      url: '/svg/important-points/illustration3.svg',
      imgCaption: data?.store?.confirmationTime,
      title: 'Approx. Confirmation Time',
    },
    {
      url: '/svg/important-points/illustration4.svg',
      imgCaption: data?.store?.missingAccepted,
      title:
        data?.store?.cashbackType === 'reward'
          ? 'Missing Rewards Acceptance'
          : 'Missing CB Acceptance',
    },
  ];

  const handleToggle = (id: number) => {
    if (activeId === id) {
      setActiveId(0);
    } else {
      setActiveId(id);
    }
  };

  const onReviewSortClick = ({ key }: { key: string }) => {
    dispatch(setReviewSortFilter(key as ReviewTypes));
    replace(
      `${pathname}?${createMultiQueryString([
        { name: 'reviewSortType', value: key },
      ])}`
    );
  };

  const handleSubmitReview = async (review: string, rating: number) => {
    try {
      const body: CreateReviewDto = {
        review: review,
        rating: rating as CreateReviewDtoRatingEnum,
        storeUid: data?.store?.uid,
      };

      await fetchWrapper('/api/proxy/stores/review/add-review', {
        method: 'POST',
        body: JSON.stringify(body),
      });

      const newReview = {
        ...body,
        createdDate: new Date().toString(),
        name: userDetails.name,
        avatar: userDetails.avatar ?? '/temp/profile.png',
        uid: 0,
      };

      toggleAddReview(false);
      userReviews.unshift(newReview);
      toast.success('Review added successfully');
      return true;
    } catch (error) {
      return false;
    }
  };

  useEffect(() => {
    setUserReviews(storeReviews.reviews);
  }, [storeReviews]);

  useEffect(() => {
    const filteredResult = filterCashbackRates(cbRatesData, searchValue);
    setFilteredCbRates(filteredResult);
  }, [searchValue, cbRatesData]);

  const handleCopyCoupon = useCopyCoupon();

  const handleCopyCouponRedirecting = (event: React.MouseEvent, item: any) => {
    event.preventDefault();
    copyToClipboard(item.couponCode);
    toast.success('Successfully copied coupon code.');
  };

  return (
    <>
      <CommonHeader headline={data?.store?.name || 'Store'} showMenuBtn />
      <div className='flex h-full lg:min-h-[calc(100svh-112px)] lg:m-[8px] !mb-0 gap-x-[8px]'>
        <AsidebarStore
          giftCard={data?.store?.giftCard}
          storeLogo={data?.store?.logo}
          storeName={data?.store?.name}
        />
        <div className='w-full'>
          <BreadcrumbSaveShare
            breadCrumbs={[
              { title: 'Cashback Home', link: '/' },
              { title: data?.store?.name },
            ]}
          />
          <StoreBanner data={data?.store} />
          <motion.div
            animate={{ opacity: 1, y: 0 }}
            className='max-w-[100vw] lg:max-w-[calc(100vw-294px)] overflow-auto scrollbarNone lg:customScrollbar bg-[#f5f5f5] dark:bg-[#2d2e32] shadow-md z-[3] sticky top-[64px] lg:top-[103px]'
            initial={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.3, ease: 'easeOut' }}
          >
            <TabsContainer
              activeId={activeTabId}
              hideCbRates={!(cbRatesData?.length > 0)}
              items={[
                {
                  id: 1,
                  label: `All Deals(${storeOffers?.pagination?.total})`,
                },
                {
                  id: 2,
                  label: `Coupons (${storeCoupons?.pagination?.total})`,
                },
                {
                  id: 3,
                  label: `${
                    data?.store?.cashbackType === 'reward'
                      ? 'Rewards'
                      : 'Cashback'
                  } Rates`,
                },
                // { id: 4, label: 'Customer Reviews' },
                { id: 5, label: 'Important Points' },
                { id: 6, label: 'Similar Stores' },
                { id: 7, label: 'Store Details' },
              ]}
              setActiveId={setActiveTabId}
            />
          </motion.div>
          <AnimatePresence mode='wait'>
            <motion.section
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              initial={{ opacity: 0, y: 10 }}
              key={activeTabId}
              transition={{ duration: 0.4, ease: 'easeInOut' }}
            >
              {activeTabId === 1 && (
                <>
                  <CommonToolbar
                    cashbackType={data?.store?.cashbackType}
                    customSortItems={dealsAndCouponsTypeSortItems}
                    initialSort={dealsAndCouponsTypeSortItems.find(
                      (item) => item.label === 'Popular'
                    )}
                    onClickFilterBtn={() =>
                      setShowFilterModal(!isShowFilterModal)
                    }
                    rootClassName='sticky top-[108px] lg:top-[147px] z-[10] mb-[10px]'
                  />
                  <div className='bg-container mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] pb-[18px]'>
                    <h4 className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite mt-2'>
                      Results{' '}
                      <span className='text-xs font-nexa font-[800]'>
                        ({storeOffers?.pagination?.total})
                      </span>
                    </h4>
                    {storeOffers?.offers?.length ? (
                      <motion.div
                        {...getOffersGridProps()}
                        animate={{ opacity: 1 }}
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        {storeOffers?.offers?.map((item, index) => (
                          <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            initial={{ opacity: 0, y: 20 }}
                            key={item.uid}
                            transition={{
                              delay: index * 0.05,
                              duration: 0.3,
                              ease: 'easeOut',
                            }}
                            whileHover={{
                              scale: 1.02,
                              transition: { duration: 0.2 },
                            }}
                          >
                            <MainOfferCard
                              duration={item.endDate}
                              hideCbTag={item.hideCbTag}
                              isAutoGenerated={item.isAutoGenerated}
                              isOfferUpto={Boolean(item?.offerCaption?.trim())}
                              offerTitle={item.offerTitle}
                              productImgUrl={item.productImage || ''}
                              rootClass='!w-full h-full'
                              saved={item.saved}
                              showNewBadge={false}
                              storeImgUrl={item.storeLogoUrl}
                              storeName={item.storeName}
                              storePopUpWarning={data?.store?.storePopUpWarning}
                              uid={item.uid}
                            >
                              {/* first child */}
                              <p
                                dangerouslySetInnerHTML={{
                                  __html: item.offerTitle,
                                }}
                              />
                              {/* second child */}
                              <p className='text-primary dark:text-white font-medium text-[9px]'>
                                {!!item.salePrice && item.salePrice > 0 && (
                                  <>
                                    Offer Applied Price
                                    <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                                      {formatIndRs(item.salePrice) || ''}
                                    </span>
                                  </>
                                )}
                              </p>
                              {/* third child */}
                              <>
                                <PlusIcon className='text-black shrink-0' />
                                <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                                  {item.offerCaption}
                                </p>
                              </>
                              {/* fourth child */}
                              {item.couponCode ? (
                                <button
                                  className='bg-primary w-full p-[4px] h-full rounded-b-[6px] relative hover:bg-primaryDark active:bg-primary'
                                  onClick={(event) =>
                                    data?.store?.storePopUpWarning
                                      ? handleCopyCouponRedirecting(event, item)
                                      : handleCopyCoupon({
                                          event,
                                          uid: item.uid,
                                          couponCode: item.couponCode,
                                        })
                                  }
                                  type='button'
                                >
                                  <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                                    <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                                      Copy Code
                                    </span>
                                    <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                                  </div>
                                </button>
                              ) : (
                                <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                                  <span className='text-[10px] font-semibold text-white'>
                                    GRAB DEAL
                                  </span>
                                </div>
                              )}
                            </MainOfferCard>
                          </motion.div>
                        ))}
                      </motion.div>
                    ) : (
                      <EnhancedNoData
                        customHeight='min-h-[400px]'
                        message='No deals available at the moment. Please check back later!'
                        showHomeLink={false}
                      />
                    )}
                  </div>
                  {storeOffers?.pagination?.pageSize > 0 && (
                    <div className='flex-center  items-center w-full my-[50px]'>
                      <ConfigProvider
                        theme={{
                          algorithm:
                            resolvedTheme === 'dark'
                              ? theme.darkAlgorithm
                              : theme.defaultAlgorithm,
                        }}
                      >
                        <Pagination
                          defaultCurrent={1}
                          defaultPageSize={15}
                          onChange={(pageNumber, pageSize) =>
                            replace(
                              `${pathname}?${createMultiQueryString([
                                { name: 'page', value: pageNumber.toString() },
                                {
                                  name: 'pageSize',
                                  value: pageSize.toString(),
                                },
                              ])}`
                            )
                          }
                          total={storeOffers.pagination.total}
                        />
                      </ConfigProvider>
                    </div>
                  )}
                </>
              )}

              {activeTabId === 2 && (
                <>
                  <CommonToolbar
                    cashbackType={data?.store?.cashbackType}
                    customSortItems={dealsAndCouponsTypeSortItems}
                    onClickFilterBtn={() => setShowFilterModal(true)}
                    rootClassName='sticky top-[108px] lg:top-[147px] z-[10] mb-[10px]'
                  />
                  <div className='bg-container pt-[10px] px-[8px] pb-[10px] lg:px-[40px] transition-all duration-300'>
                    <h4 className='hidden lg:block text-[14px] font-pat font-normal text-blackWhite mt-2'>
                      Results{' '}
                      <span className='text-xs font-nexa font-[800]'>
                        ({storeCoupons?.pagination?.total})
                      </span>
                    </h4>
                    {storeCoupons?.offers?.length ? (
                      <motion.div
                        {...getOffersGridProps()}
                        animate={{ opacity: 1 }}
                        className='transition-all duration-300 mt-2'
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        {storeCoupons?.offers?.map((item, index) => (
                          <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            initial={{ opacity: 0, y: 20 }}
                            key={item.uid}
                            transition={{
                              delay: index * 0.05,
                              duration: 0.3,
                              ease: 'easeOut',
                            }}
                            whileHover={{
                              scale: 1.02,
                              transition: { duration: 0.2 },
                            }}
                          >
                            <MainOfferCard
                              duration={item.endDate}
                              isOfferUpto={Boolean(item?.offerCaption?.trim())}
                              offerTitle={item.offerTitle}
                              productImgUrl={item.productImage || ''}
                              saved={item.saved}
                              showNewBadge={false}
                              storeImgUrl={item.storeLogoUrl}
                              storeName={item.storeName}
                              storePopUpWarning={data?.store?.storePopUpWarning}
                              uid={item.uid}
                            >
                              {/* first child */}
                              <p
                                dangerouslySetInnerHTML={{
                                  __html: item.offerTitle,
                                }}
                              />
                              {/* second child */}
                              <p className='text-primary dark:text-white font-medium text-[9px]'>
                                {item.salePrice && item.salePrice > 0 && (
                                  <>
                                    Offer Applied Price
                                    <span className='font-nexa font-black ml-[4px] text-[10px] sm:text-[11px]'>
                                      {formatIndRs(item.salePrice)}
                                    </span>
                                  </>
                                )}
                              </p>
                              {/* third child */}
                              <>
                                <PlusIcon className='text-black shrink-0' />
                                <p className='text-[10px] text-black font-bold leading-none ml-[4px] mt-[2px] truncate'>
                                  {item.offerCaption}
                                </p>
                              </>
                              {/* fourth child */}
                              {item.couponCode ? (
                                <button
                                  className='bg-primary w-full p-[4px] h-full rounded-b-[6px] relative hover:bg-primaryDark active:bg-primary'
                                  onClick={(event) =>
                                    data?.store?.storePopUpWarning
                                      ? handleCopyCouponRedirecting(event, item)
                                      : handleCopyCoupon({
                                          event,
                                          uid: item.uid,
                                          couponCode: item.couponCode,
                                        })
                                  }
                                  type='button'
                                >
                                  <div className='border-dashed border-[0.5px] border-[#E0DCFF] rounded-b-[4px] h-full flex items-center justify-center'>
                                    <span className='text-[10px] sm:text-[12px] font-semibold text-white'>
                                      Copy Code
                                    </span>
                                    <CopySVG className='text-white absolute top-[16px] right-[12px]' />
                                  </div>
                                </button>
                              ) : (
                                <div className='bg-primary p-[4px] h-full rounded-b-[6px] relative flex-center'>
                                  <span className='text-[10px] font-semibold text-white'>
                                    GRAB DEAL
                                  </span>
                                </div>
                              )}
                            </MainOfferCard>
                          </motion.div>
                        ))}
                      </motion.div>
                    ) : (
                      <EnhancedNoData
                        customHeight='min-h-[400px]'
                        message='No coupons available at the moment. Please check back later!'
                        showHomeLink={false}
                      />
                    )}
                  </div>
                  {storeCoupons?.pagination?.pageSize > 0 && (
                    <div className='flex-center  items-center w-full my-[50px]'>
                      <ConfigProvider
                        theme={{
                          algorithm:
                            resolvedTheme === 'dark'
                              ? theme.darkAlgorithm
                              : theme.defaultAlgorithm,
                        }}
                      >
                        <Pagination
                          defaultCurrent={1}
                          defaultPageSize={15}
                          onChange={(pageNumber, pageSize) =>
                            replace(
                              `${pathname}?${createMultiQueryString([
                                { name: 'page', value: pageNumber.toString() },
                                {
                                  name: 'pageSize',
                                  value: pageSize.toString(),
                                },
                              ])}`
                            )
                          }
                          total={storeCoupons.pagination.total}
                        />
                      </ConfigProvider>
                    </div>
                  )}
                </>
              )}

              {activeTabId === 3 && (
                <>
                  <div className='min-h-[56px] flex lg:justify-end items-center bg-[#E1E2E4] dark:bg-container lg:!bg-container lg:h-fit px-[8px] lg:px-[40px] sticky top-[108px] lg:top-[147px] transition-all duration-300 z-[10] shadow-sm'>
                    <SearchInput
                      onChange={(value) => setSearchValue(value)}
                      value={searchValue}
                    />
                  </div>

                  <div className='pt-[16px] mx-[6px] lg:mx-0 lg:px-[40px] pb-[10vh]'>
                    {filteredCbRates.length > 0 ? (
                      <motion.div
                        animate={{ opacity: 1 }}
                        className='flex flex-col gap-[10px] lg:gap-[18px]'
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        {filteredCbRates.map((item, index) => (
                          <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            initial={{ opacity: 0, y: 20 }}
                            key={item.uid}
                            transition={{
                              delay: index * 0.05,
                              duration: 0.3,
                              ease: 'easeOut',
                            }}
                          >
                            <CbRatesAccordian
                              activeId={activeId}
                              data={{
                                id: index + 1,
                                uid: item.uid,
                                question: item.name,
                                answer: item.description,
                                newUser: item.newUserRate,
                                oldUser: item.oldUserRate,
                                cashbackType: item.type,
                                storeName: data.store.name,
                                storeLogo: data.store.logo,
                              }}
                              onClick={handleToggle}
                            />
                          </motion.div>
                        ))}
                      </motion.div>
                    ) : (
                      <EnhancedNoData
                        customHeight='min-h-[400px]'
                        message='No cashback rates available at the moment.'
                        showHomeLink={false}
                      />
                    )}
                  </div>
                </>
              )}

              {activeTabId === 4 && (
                <>
                  <div className='min-h-[56px] flex justify-between items-center bg-[#E1E2E4] dark:bg-container lg:!bg-container lg:h-fit px-[8px] lg:px-[40px] sticky top-[108px] lg:top-[147px] lg:pt-[25px] lg:pb-[15px] z-[10] transition-all duration-300 shadow-sm'>
                    <div className='flex'>
                      <div
                        className='h-[32px] lg:h-[36px] bg-white dark:bg-[#3D4049] min-w-[208px] lg:min-w-[265px] px-[10px] flex items-center justify-evenly rounded-[5px] text-blackWhite'
                        style={{
                          boxShadow: '0px 4px 4px 0px rgba(0, 0, 0, 0.07)',
                        }}
                      >
                        <span className='text-[9px] lg:text-xs font-normal '>
                          Overall
                        </span>
                        <div className='ml-[5px] h-[13px] border-l-[1px] border-[#ccc] pl-[10px]'>
                          <RatingStars
                            rating={data?.store?.ratingAverage}
                            wrapperClassName='!gap-x-[2px] lg:gap-x-[5px]'
                          />
                        </div>
                        <div className='ml-[10px] text-[8px] lg:text-xs font-normal'>
                          <span className='font-black font-nexa '>
                            {data?.store?.ratingAverage ?? 4}
                          </span>
                          <span className='font-bold'> / </span>
                          <span className=''>
                            {data?.store?.ratingsCount ?? 5}
                          </span>{' '}
                          Rating
                        </div>
                      </div>
                      <button
                        className='ml-[8px] hidden lg:flex w-[122px] h-[36px] border-[2px] border-primary rounded-[5px] items-center justify-center text-primary text-xs font-bold cursor-pointer transition-transform duration-300 hover:scale-105 active:scale-95'
                        onClick={() => {
                          if (!isUserLogin) {
                            dispatch(setLoginModalOpen(true));
                          } else {
                            toggleAddReview(!addReviewShown);
                          }
                        }}
                        type='button'
                      >
                        Add Review
                      </button>
                    </div>
                    <div>
                      <ToolbarDropdown
                        items={reviewSortItems}
                        name='Sort'
                        onClick={onReviewSortClick}
                      >
                        <SortSVG className='w-[14px] lg:w-[20px] text-[#7366D9] dark:text-white transition-transform duration-300 hover:scale-110' />
                      </ToolbarDropdown>
                    </div>
                  </div>
                  <div className='bg-container pt-[16px] px-[7px] mx-[6px] lg:mx-0 lg:px-[40px] transition-all duration-300'>
                    {addReviewShown && (
                      <div className='hidden lg:block'>
                        <CustomerReviewInputCont
                          onClickSubmit={handleSubmitReview}
                        />
                      </div>
                    )}
                    {storeReviews?.reviews?.length > 0 ? (
                      <motion.div
                        animate={{ opacity: 1 }}
                        className='flex flex-col gap-y-[10px] relative lg:pt-[20px] pb-[8vh]'
                        initial={{ opacity: 0 }}
                        transition={{ duration: 0.5 }}
                      >
                        {storeReviews?.reviews?.map((item, index) => (
                          <motion.div
                            animate={{ opacity: 1, y: 0 }}
                            initial={{ opacity: 0, y: 20 }}
                            key={item.uid || index}
                            transition={{
                              delay: index * 0.05,
                              duration: 0.3,
                              ease: 'easeOut',
                            }}
                            whileHover={{
                              scale: 1.01,
                              transition: { duration: 0.2 },
                            }}
                          >
                            <CustomerReview
                              avatar={item?.avatar}
                              date={item.createdDate}
                              name={item?.name}
                              rating={item?.rating}
                              reviewText={item?.review}
                            />
                          </motion.div>
                        ))}
                        {storeReviews?.pagination?.pageSize > 0 && (
                          <div className='flex-center w-full my-[50px]'>
                            <ConfigProvider
                              theme={{
                                algorithm:
                                  resolvedTheme === 'dark'
                                    ? theme.darkAlgorithm
                                    : theme.defaultAlgorithm,
                              }}
                            >
                              <Pagination
                                defaultCurrent={1}
                                defaultPageSize={15}
                                onChange={(pageNumber, pageSize) =>
                                  replace(
                                    `${pathname}?${createMultiQueryString([
                                      {
                                        name: 'page',
                                        value: pageNumber.toString(),
                                      },
                                      {
                                        name: 'pageSize',
                                        value: pageSize.toString(),
                                      },
                                    ])}`
                                  )
                                }
                                total={storeReviews.pagination.total}
                              />
                            </ConfigProvider>
                          </div>
                        )}

                        <button
                          className='w-[40px] h-[40px] rounded-full bg-primary overflow-hidden flex-center fixed bottom-[8%] right-[20px] lg:hidden drop-shadow-md transition-transform duration-300 hover:scale-110 active:scale-95'
                          onClick={() => setOpenReviewModal(true)}
                          type='button'
                        >
                          <span className='text-white text-[30px]'>+</span>
                        </button>
                      </motion.div>
                    ) : (
                      <EnhancedNoData
                        customHeight='min-h-[400px]'
                        message='No reviews available at the moment.'
                        showHomeLink={false}
                      />
                    )}
                  </div>

                  <BottomDrawer
                    heightClass='40svh'
                    onClose={() => setOpenReviewModal(!openReviewModal)}
                    open={openReviewModal}
                    title='Write a Review'
                    titleIcon={
                      <Image
                        alt='icon'
                        height={12}
                        src='/svg/text-icon.svg'
                        width={12}
                      />
                    }
                    topClass='calc(100% - 40svh)'
                  >
                    <CustomerReviewInputCont
                      onClickSubmit={handleSubmitReview}
                    />
                  </BottomDrawer>
                </>
              )}

              {activeTabId === 5 && (
                <>
                  <motion.div
                    animate={{ opacity: 1 }}
                    className={
                      'bg-container grid grid-cols-2 gap-x-[8px] gap-y-[10px] lg:flex lg:flex-wrap lg:gap-x-[20px] p-[15px] lg:py-[40px] lg:justify-center transition-all duration-300'
                    }
                    initial={{ opacity: 0 }}
                    transition={{ duration: 0.5 }}
                  >
                    {importantPoints.map((item, index) => (
                      <motion.div
                        animate={{ opacity: 1, y: 0 }}
                        initial={{ opacity: 0, y: 20 }}
                        key={item.title}
                        transition={{
                          delay: index * 0.1,
                          duration: 0.4,
                          ease: 'easeOut',
                        }}
                        whileHover={{
                          scale: 1.05,
                          transition: { duration: 0.2 },
                        }}
                      >
                        <ImportantPointCard
                          imgCaption={item.imgCaption}
                          imgUrl={item.url}
                          title={item.title}
                        />
                      </motion.div>
                    ))}
                  </motion.div>

                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className='rounded-[10px] bg-container text-blackWhite py-[20px] px-[15px] mx-[6px] lg:mx-0 lg:px-[40px] mb-[10vh] transition-all duration-300'
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.3, duration: 0.5 }}
                  >
                    <motion.div
                      className='flex items-center'
                      transition={{
                        type: 'spring',
                        stiffness: 400,
                        damping: 10,
                      }}
                      whileHover={{ x: 5 }}
                    >
                      <ImportantUpdateBadge className='text-primary dark:text-white' />
                      <span className='ml-[7px] text-[9px] font-semibold text-primary lg:text-xs'>
                        Important Update
                      </span>
                    </motion.div>
                    <motion.p
                      animate={{ opacity: 1 }}
                      className='mt-[16px] text-[8px] sm:text-[9px] lg:text-xs leading-[17px] lg:leading-[24px]'
                      initial={{ opacity: 0 }}
                      transition={{ delay: 0.5, duration: 0.5 }}
                    >
                      <b>Bulk Purchases are strictly NOT allowed.</b>{' '}
                    </motion.p>
                  </motion.div>
                </>
              )}

              {activeTabId === 6 && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='bg-container mx-[6px] px-[8px] lg:mx-0 lg:pt-[16px] lg:px-[40px] pb-[18px] transition-all duration-300'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  {data?.similarStores?.length > 0 ? (
                    <motion.div
                      {...getOffersGridProps()}
                      animate={{ opacity: 1 }}
                      className='transition-all duration-300 gap-4 lg:gap-6'
                      initial={{ opacity: 0 }}
                      transition={{ duration: 0.5 }}
                    >
                      {data?.similarStores.map((item, index) => (
                        <motion.div
                          animate={{ opacity: 1, y: 0 }}
                          initial={{ opacity: 0, y: 20 }}
                          key={item.uid}
                          transition={{
                            delay: index * 0.05,
                            duration: 0.3,
                            ease: 'easeOut',
                          }}
                          whileHover={{
                            scale: 1.05,
                            transition: { duration: 0.2 },
                          }}
                        >
                          <StoreByCBCard
                            bgColor={item.bgColor}
                            caption={item.caption}
                            fromSavedScreen={false}
                            saved={item.saved}
                            src={item.imageUrl}
                            storeName={item.storeName}
                            storePopUpWarning={data?.store?.storePopUpWarning}
                            uid={item.uid}
                          />
                        </motion.div>
                      ))}
                    </motion.div>
                  ) : (
                    <EnhancedNoData
                      customHeight='min-h-[400px]'
                      message='No similar stores found.'
                      showHomeLink={false}
                    />
                  )}
                </motion.div>
              )}

              {activeTabId === 7 && (
                <motion.div
                  animate={{ opacity: 1 }}
                  className='bg-container mx-[6px] px-[15px] lg:mx-0 lg:pt-[16px] lg:px-[40px] pb-[18px] transition-all duration-300'
                  initial={{ opacity: 0 }}
                  transition={{ duration: 0.5 }}
                >
                  <motion.div
                    animate={{ opacity: 1, y: 0 }}
                    className='py-4'
                    initial={{ opacity: 0, y: 20 }}
                    transition={{ delay: 0.2, duration: 0.5 }}
                  >
                    <motion.h3
                      animate={{ opacity: 1 }}
                      className='text-sm font-semibold text-primary mb-3 transition-colors duration-200'
                      initial={{ opacity: 0 }}
                      transition={{ delay: 0.3, duration: 0.5 }}
                    >
                      About {data?.store?.name}
                    </motion.h3>
                    {data?.store?.description ? (
                      <motion.div
                        animate={{ opacity: 1 }}
                        className='font-normal text-xs text-blackWhite break-words transition-all duration-300'
                        dangerouslySetInnerHTML={{
                          __html: data?.store?.description,
                        }}
                        initial={{ opacity: 0 }}
                        style={{ wordBreak: 'break-word' }}
                        transition={{ delay: 0.4, duration: 0.5 }}
                      />
                    ) : (
                      <motion.p
                        animate={{ opacity: 1 }}
                        className='text-xs text-gray-500 italic'
                        initial={{ opacity: 0 }}
                        transition={{ delay: 0.4, duration: 0.5 }}
                      >
                        No description available.
                      </motion.p>
                    )}
                  </motion.div>
                </motion.div>
              )}
            </motion.section>
          </AnimatePresence>
          {/* Share & Earn Banner */}
          {[
            'Flipkart',
            'Ajio',
            'Myntra',
            'Shopsy',
            'Croma',
            'TaTa CliQ',
          ].includes(data?.store?.name) && (
            <div className='mb-4'>
              <ShareEarnBanner />
            </div>
          )}
        </div>
      </div>
      <CommonFilterMobile
        filterProps={[{ filter: 'user' }]}
        isShowFilterModal={isShowFilterModal}
        setShowFilterModal={() => setShowFilterModal(!isShowFilterModal)}
      />
    </>
  );
};

export default IndexStoreClients;
