import React from 'react';
import IndexClientsTMC from './index-clients';
import { BASE_URL } from '@/config';
import fetchWrapper from '@/utils/fetch-wrapper';
import { toast } from 'react-toastify';
import { Metadata } from 'next';
import { generateFAQSchema } from '@/utils/schema';

export const metadata: Metadata = {
  title: 'Terms & Conditions - IndianCashback',
  description:
    'Understand the terms and conditions for using IndianCashback services. Learn about cashback eligibility, payment processes, account management, and your rights and responsibilities as a user of our cashback platform.',
  alternates: {
    canonical: 'https://www.indiancashback.com/terms-and-conditions',
  },
  openGraph: {
    url: 'https://www.indiancashback.com/terms-and-conditions',
    title: 'Terms & Conditions - IndianCashback',
    description:
      'Understand the terms and conditions for using IndianCashback services. Learn about cashback eligibility, payment processes, account management, and your rights and responsibilities as a user of our cashback platform.',
  },
};

async function getTermsAndConditionsData() {
  const res = await fetchWrapper<string>(
    `${BASE_URL}/context/terms-and-privacy?type=terms`,
    {
      responseType: 'text',
    }
  );
  return res;
}

const Page = async () => {
  let resData: string;
  try {
    resData = await getTermsAndConditionsData();
  } catch (err: any) {
    toast.error('Failed to fetch terms and conditions data');
    console.log({ err });
    return (
      <div className="error-container">
        <h1>Error loading page</h1>
        <p>Please try again later.</p>
      </div>
    );
  }

  // Create FAQ schema for common terms and conditions questions
  const faqSchema = generateFAQSchema({
    mainEntity: [
      {
        '@type': 'Question',
        name: 'What is IndianCashback.com?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'IndianCashback.com is one of the leading cashback websites in India that offers deals, coupons, and cashback on online shopping across various stores and categories.',
        },
      },
      {
        '@type': 'Question',
        name: 'How does IndianCashback.com work?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: "When you shop through IndianCashback.com, we earn a commission from the store. We share a portion of this commission with you as cashback. Simply click through our links to the store, make a purchase, and we'll track and credit the cashback to your account.",
        },
      },
      {
        '@type': 'Question',
        name: 'When will I receive my cashback?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'Cashback typically goes through different stages: Pending, Tracked, and Confirmed. Once confirmed, it becomes available for withdrawal. The timeline varies by store and can take anywhere from 30 to 90 days, depending on store policies.',
        },
      },
      {
        '@type': 'Question',
        name: 'How can I withdraw my cashback?',
        acceptedAnswer: {
          '@type': 'Answer',
          text: 'You can withdraw your confirmed cashback through various methods including bank transfer, UPI, or as gift cards. Minimum withdrawal amounts and processing times may apply depending on the method chosen.',
        },
      },
      {
        '@type': 'Question',
        name: "What if my cashback isn't tracked?",
        acceptedAnswer: {
          '@type': 'Answer',
          text: "If your cashback isn't tracked within the expected timeframe, you can raise a missing cashback claim through your account. Our support team will investigate and help resolve the issue.",
        },
      },
    ],
  });

  return (
    <>
      <script
        dangerouslySetInnerHTML={{ __html: faqSchema }}
        type='application/ld+json'
      />
      <IndexClientsTMC data={resData} />
    </>
  );
};

export default Page;

// ISR Configuration
// Revalidate every 12 hours (43200 seconds)
// Terms and conditions content changes infrequently but should be kept reasonably fresh
export const revalidate = 43200;
