'use client';
import React from 'react';
import CommonHeader from '../components/headers/common-header';
import BreadcrumbSaveShare from '../components/atoms/breadcrumb-container';
import Image from 'next/image';
import TermsImg from '@/public/img/terms.png';

const IndexClientsTMC = ({ data }: { data: string }) => {
  return (
    <>
      <CommonHeader headline='Terms and Conditions' />
      <section className='header-container'>
        <BreadcrumbSaveShare
          breadCrumbs={[
            { title: 'Cashback Home', link: '/' },
            { title: 'Terms and Conditions' },
          ]}
        />

        <div className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] bg-container lg:bg-[#E0E0E0] lg:bg-container'>
          <div className='mx-auto'>
            <Image
              alt='terms_and_conditions'
              className='mx-auto'
              src={TermsImg}
            />
          </div>
          <div
            className='mx-[6px] px-[8px] lg:mx-0 lg:px-[40px] py-[26px] overflow-hidden bg-container lg:bg-[#E0E0E0] lg:bg-container text-sm'
            dangerouslySetInnerHTML={{ __html: data }}
          />
        </div>
      </section>
    </>
  );
};

export default IndexClientsTMC;
