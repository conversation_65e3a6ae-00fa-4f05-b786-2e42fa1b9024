# =============================================================================
# .gitignore for Next.js PWA with Android TWA Support
# =============================================================================
# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# =============================================================================
# Dependencies and Package Managers
# =============================================================================

# Node.js dependencies
node_modules/
/.pnp
.pnp.js
.pnp.loader.mjs

# Package manager caches
.npm/
.yarn/
.yarn-integrity
.pnpm-store/

# Bun
.bun/
# Note: bun.lockb should be committed for Bun projects

# Lock files (keep package-lock.json for npm projects, ignore others)
yarn.lock
pnpm-lock.yaml

# =============================================================================
# Build Outputs and Artifacts
# =============================================================================

# Next.js build outputs
/.next/
/out/
/build/
/dist/

# TypeScript build info
*.tsbuildinfo
next-env.d.ts

# PWA and Service Worker files
public/workbox-*
public/sw.*
public/sw.js.map
workbox-*.js
sw.js
sw.js.map

# Android TWA build outputs
*.apk
*.aab
*.idsig
*-unsigned-aligned.apk
*-release-signed.apk
*-release-bundle.aab
app/build/
build/intermediates/
build/outputs/
build/reports/
build/tmp/
local.properties

# Gradle
.gradle/
gradle-app.setting
!gradle-wrapper.jar
!gradle-wrapper.properties
!gradle/wrapper/gradle-wrapper.jar
!gradle/wrapper/gradle-wrapper.properties

# General build artifacts
/lib/
/es/
*.buildinfo

# =============================================================================
# Environment and Configuration Files
# =============================================================================

# Environment files
.env
.env*.local
.env.development
.env.production
.env.test

# Certificates and security
certificates/
*.pem
*.key
*.crt
*.p12
*.keystore

# =============================================================================
# Development Tools and IDEs
# =============================================================================

# Visual Studio Code
.vscode/
!.vscode/settings.json
!.vscode/tasks.json
!.vscode/launch.json
!.vscode/extensions.json
*.code-workspace

# JetBrains IDEs
.idea/
*.iws
*.iml
*.ipr

# Visual Studio
*.suo
*.user
*.userosscache
*.sln.docstates
*.userprefs
.vs/

# Sublime Text
*.sublime-workspace
*.sublime-project

# Atom
.atom/

# Vim
*.swp
*.swo
*~
.netrwhist

# Emacs
\#*\#
/.emacs.desktop
/.emacs.desktop.lock
*.elc
auto-save-list
tramp
.\#*

# Development tool caches
.eslintcache
.stylelintcache

# =============================================================================
# Testing and Coverage
# =============================================================================

# Test coverage
/coverage/
/.nyc_output/
coverage.lcov
*.lcov

# Jest
jest.config.js.cache
.jest-cache/

# Playwright
/test-results/
/playwright-report/
/playwright/.cache/

# Cypress
cypress/videos/
cypress/screenshots/
cypress/downloads/

# Testing artifacts
*.tgz
test-output/
test-results.xml
junit.xml

# =============================================================================
# Logs and Temporary Files
# =============================================================================

# Logs
logs/
*.log
npm-debug.log*
yarn-debug.log*
yarn-error.log*
pnpm-debug.log*
lerna-debug.log*
dev-debug.log

# Temporary files
tmp/
temp/
.tmp/
.temp/
*.pid
*.seed
*.pid.lock

# =============================================================================
# Cache Directories
# =============================================================================

# Various caches
.cache/
.parcel-cache/
.next/cache/
.nuxt/
.vuepress/dist/
.serverless/
.fusebox/

# =============================================================================
# Operating System Files
# =============================================================================

# macOS
.DS_Store
.DS_Store?
._*
.Spotlight-V100
.Trashes

# Windows
Thumbs.db
Thumbs.db:encryptable
ehthumbs.db
ehthumbs_vista.db
*.tmp
*.temp
Desktop.ini
$RECYCLE.BIN/
*.cab
*.msi
*.msix
*.msm
*.msp

# Linux
.fuse_hidden*
.directory
.Trash-*
.nfs*

# =============================================================================
# Deployment and Cloud Services
# =============================================================================

# Vercel
.vercel

# Netlify
.netlify/

# Firebase
.firebase/
firebase-debug.log*
firestore-debug.log*

# =============================================================================
# Documentation and Static Site Generators
# =============================================================================

# Docusaurus
.docusaurus/
/build/

# VuePress
.vuepress/dist/

# GitBook
_book/

# =============================================================================
# Task Management and Project Files
# =============================================================================

# Task files (project-specific)
tasks.json
tasks/

# Project files
*.project
.project
.settings/

# =============================================================================
# Miscellaneous
# =============================================================================

# Runtime data
pids/
*.pid
*.seed
*.pid.lock

# Optional npm cache directory
.npm

# Optional eslint cache
.eslintcache

# Optional REPL history
.node_repl_history

# Output of 'npm pack'
*.tgz

# Yarn Integrity file
.yarn-integrity

# dotenv environment variables file
.env.local
.env.development.local
.env.test.local
.env.production.local

# Stores VSCode versions used for testing VSCode extensions
.vscode-test

# yarn v2
.yarn/cache
.yarn/unplugged
.yarn/build-state.yml
.yarn/install-state.gz
.pnp.*
app/src/main/res/xml/shortcuts.xml
